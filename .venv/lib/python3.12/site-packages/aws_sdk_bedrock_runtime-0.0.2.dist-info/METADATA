Metadata-Version: 2.4
Name: aws_sdk_bedrock_runtime
Version: 0.0.2
Summary: aws_sdk_bedrock_runtime client
License: Apache-2.0
License-File: LICENSE
License-File: NOTICE
Keywords: aws_sdk_bedrock_runtime,smithy
Classifier: Development Status :: 2 - Pre-Alpha
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.12
Requires-Dist: smithy-aws-core<0.1.0
Requires-Dist: smithy-aws-event-stream<0.1.0
Requires-Dist: smithy-core<0.1.0
Requires-Dist: smithy-http[awscrt]<0.1.0
Requires-Dist: smithy-<PERSON><PERSON><0.1.0
Provides-Extra: docs
Requires-Dist: pydata-sphinx-theme>=0.16.1; extra == 'docs'
Requires-Dist: sphinx>=8.2.3; extra == 'docs'
Provides-Extra: tests
Requires-Dist: pytest-asyncio<0.21.0,>=0.20.3; extra == 'tests'
Requires-Dist: pytest<8.0.0,>=7.2.0; extra == 'tests'
Description-Content-Type: text/markdown

## Amazon Bedrock Runtime Client

The `aws_sdk_bedrock_runtime` client is still under active developement.
Changes may result in breaking changes prior to the release of version
1.0.0.

### Documentation

Documentation is available in the `/docs` directory of this package.
Pages can be built into portable HTML files for the time being. You can
follow the instructions in the docs [README.md](https://github.com/awslabs/aws-sdk-python/blob/main/clients/aws-sdk-bedrock-runtime/docs/README.md).

For high-level documentation, you can view the [`dev-guide`](https://github.com/awslabs/aws-sdk-python/tree/main/dev-guide) at the top level of this repo.
