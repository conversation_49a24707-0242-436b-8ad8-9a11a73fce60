../../../bin/black,sha256=LRMMBKRhnNe160WCwFMNSxEyVL3EKtJ-S8uw1rUheR8,250
../../../bin/blackd,sha256=JH6ooG0kj7eco7gfu7RuqvEalIeQDSLoCnnNpL1yS6s,251
30fcd23745efe32ce681__mypyc.cpython-312-darwin.so,sha256=EKNLsxPF3ksHuIBCcG0H3Rmk9OLsrf46U3NWCRf1JHU,4096368
__pycache__/_black_version.cpython-312.pyc,,
_black_version.py,sha256=sVEDQEx6fAtLwbQSaty477mqXtjh32UVgbeV3rPRIvo,19
black-24.4.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-24.4.2.dist-info/METADATA,sha256=IvFktiCXcCQRtGq12tLvVQ34JUmaWDr5UEZGF_3fQR4,77124
black-24.4.2.dist-info/RECORD,,
black-24.4.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-24.4.2.dist-info/WHEEL,sha256=jNCOgKUTIbkPB24lOuwjoXUrhqvn3v6xho45URz0ITE,105
black-24.4.2.dist-info/entry_points.txt,sha256=XTCA4X2yVA0tMiV7l96Gv9TyxhVhoCaznLN2XThqYSA,144
black-24.4.2.dist-info/licenses/AUTHORS.md,sha256=8drxTtCp41j9z9NFJ9U37R1m9qL0zwTMELvgHFFkwao,8092
black-24.4.2.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.cpython-312-darwin.so,sha256=Sr8FBdc9yGGXEyv0yG63crLpPAhAB8Qkc2sxpF8_mwU,50144
black/__init__.py,sha256=XV5ft9pudPRwoZDq9dH2Ytz4X-Ggi8_AzPu-tWMrp7U,51931
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/__pycache__/__init__.cpython-312.pyc,,
black/__pycache__/__main__.cpython-312.pyc,,
black/__pycache__/_width_table.cpython-312.pyc,,
black/__pycache__/brackets.cpython-312.pyc,,
black/__pycache__/cache.cpython-312.pyc,,
black/__pycache__/comments.cpython-312.pyc,,
black/__pycache__/concurrency.cpython-312.pyc,,
black/__pycache__/const.cpython-312.pyc,,
black/__pycache__/debug.cpython-312.pyc,,
black/__pycache__/files.cpython-312.pyc,,
black/__pycache__/handle_ipynb_magics.cpython-312.pyc,,
black/__pycache__/linegen.cpython-312.pyc,,
black/__pycache__/lines.cpython-312.pyc,,
black/__pycache__/mode.cpython-312.pyc,,
black/__pycache__/nodes.cpython-312.pyc,,
black/__pycache__/numerics.cpython-312.pyc,,
black/__pycache__/output.cpython-312.pyc,,
black/__pycache__/parsing.cpython-312.pyc,,
black/__pycache__/ranges.cpython-312.pyc,,
black/__pycache__/report.cpython-312.pyc,,
black/__pycache__/rusty.cpython-312.pyc,,
black/__pycache__/schema.cpython-312.pyc,,
black/__pycache__/strings.cpython-312.pyc,,
black/__pycache__/trans.cpython-312.pyc,,
black/_width_table.cpython-312-darwin.so,sha256=Hda8yvHdT7wQ3DxQblc4d_ApKssz_7JCPjUbC8iE7qo,50160
black/_width_table.py,sha256=2lSnE4s_nVXXfIj9hP2qWASqX8I003WxBM5xPnelDrQ,10761
black/brackets.cpython-312-darwin.so,sha256=aXpntwSCE4nyuOiE_rXECKtKlD66_c7eIifTCa0R8ME,50144
black/brackets.py,sha256=vSDU10V3cCzAqYO-XE9pztRUx49AlafLRCECApzGzsw,12426
black/cache.cpython-312-darwin.so,sha256=8UtQjgOPntavRkWNk9RaSDdGsRiG8OShY4OBgV9Y5ek,50136
black/cache.py,sha256=5wXB2noRzTIfp4Fm_XFu2GyX3kxcHDzwVtwKv4UY2BM,4835
black/comments.cpython-312-darwin.so,sha256=4OO-LI3Lb-Rj5ycJPttMdyJ2Djv0zaotpygJeEUdFgQ,50144
black/comments.py,sha256=9CBGkhZ2s-T1A-Y0CnHoRocQK1R1T4RlllCRxyxp_-U,15983
black/concurrency.py,sha256=oyFRSg5wisTi2dNLKig6Cm7R2uHWVwkfLbI6pCZRNCo,6410
black/const.cpython-312-darwin.so,sha256=VWvRcFfy7CnzfWN5QGj7tVAuUBYUVwEjQ0eXtpR6CeY,50136
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=HmpJna5KhwLpgQkqEGR2FX6GRmJ2tFh4Jl6X2aMR3ak,1906
black/files.py,sha256=xZzXfmDf2geWZnjoOW-KyY6BZieUw9jLYm86pzdYW0Y,14665
black/handle_ipynb_magics.cpython-312-darwin.so,sha256=erjlolzL7FlXPUL8ygfwohbe593Wv_sslPnn1HmI89A,50184
black/handle_ipynb_magics.py,sha256=eAULCQCmQRbgluSRu5jFQNtILMpXnjZWNoOVQLiaEJ8,13364
black/linegen.cpython-312-darwin.so,sha256=EcCZgw9hbX6_5_eO8-ElWkdpCzRQe_HT2R_SnwWEYEE,50144
black/linegen.py,sha256=CLXgK9ck7b06MAcgH3egSiEcWurzNWn5Hx0SkBzgoGo,68966
black/lines.cpython-312-darwin.so,sha256=USIafkD3wMnz4tukAQQ1UibDwyKxmPJJCLf6nwmveuA,50136
black/lines.py,sha256=ncrM1J5V_soPNe3ZKC8EsProWBtglAM3i-5iwkWu_l8,39492
black/mode.cpython-312-darwin.so,sha256=UrfMFa7ZxYfst7-eri4raNBNe9t2d02lriyzpjnSOfk,50136
black/mode.py,sha256=jcyx0eH4_sGCpAffl-hEEQSlo6miJRdaIdhWG7Jao8s,9313
black/nodes.cpython-312-darwin.so,sha256=DQxkfEgzm3zLrNDWgbgPOodB9qH5-o7PmfcxxixxC98,50136
black/nodes.py,sha256=yKfYbhW6vxU6HUebZP5v9xSTEx2WWZ4HRenpvhlo9lA,29742
black/numerics.cpython-312-darwin.so,sha256=Q86Mdto7eF1aQvmp5fnr3jp-BaBbeI4Jy4GjUGYJqoM,50144
black/numerics.py,sha256=xRGnTSdMVbTaA9IGechc8JM-cIuJGCc326s71hkXJIw,1655
black/output.py,sha256=hhk6746lZBMbGuJ1VLgjl_Jrn7Yvb24ZBFaCfpO_ijM,3939
black/parsing.cpython-312-darwin.so,sha256=GwMsYc5vW8OAFIbWqFXh-trbRBs7XBx2yHSnGHri5hI,50144
black/parsing.py,sha256=R1ActEaqZWI-UrwNTSn3FVuBDQWl674somlnIqC_hSQ,8428
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.cpython-312-darwin.so,sha256=-TOYXkeUkrXCsRojA99Y11PqQ-_C5AcmOuGiCcLPbt4,50144
black/ranges.py,sha256=EHKl3_aswNmDw4AMq9GZ3PVvBo42nC0RU5ZVMt4RdzU,19695
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/resources/__init__.cpython-312-darwin.so,sha256=9TWyIELm20KxpXp7AUTMjNv-u81GUaKdhXnwkmUfNn0,50160
black/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/resources/__pycache__/__init__.cpython-312.pyc,,
black/resources/black.schema.json,sha256=Y6N2HBCLAsfchkg52rQsBBRJhEeQ4BVFp_tQGLc7Jik,7452
black/rusty.cpython-312-darwin.so,sha256=NRbhZ95z3BRJxv_9Apv9rQ5KyyrcQN7u2v37x87xo6I,50136
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/schema.cpython-312-darwin.so,sha256=dNbNaytxbvTfeKmZAYVxTNwC_chnJKrPVze3YqkqDes,50144
black/schema.py,sha256=PiSQ5HSrqFmduqC_2_-T_-5GQ8Fr-IDYG6kbyiC_OyY,617
black/strings.cpython-312-darwin.so,sha256=DupahsQ-WepmjXVON8CXE3klFhnXZ-z0BZ0bXWhjz4E,50144
black/strings.py,sha256=9S1jnAXm8iDlN-gSlBFN8A7QWu8fxtysX6DBhWcwEek,13172
black/trans.cpython-312-darwin.so,sha256=Z7yiv4yJiO2qxHir25J1yUbnxUBl1GxfUJxh2XAb-io,50136
black/trans.py,sha256=soTQm8RpSKR9nNePsK5a4iocTaE0Dwciw70iRKqz8a8,95476
blackd/__init__.py,sha256=Xqqesi8iCAtLvZSQ-DDahAi6xZAxUthexvFKC-8zFvg,8867
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/__pycache__/__init__.cpython-312.pyc,,
blackd/__pycache__/__main__.cpython-312.pyc,,
blackd/__pycache__/middlewares.cpython-312.pyc,,
blackd/middlewares.py,sha256=QS7cs86Ojuaqh64dGneimhJ-f30rDI646c27ts4Dwh0,1585
blib2to3/Grammar.txt,sha256=zjM1rSC9GJjnboYyRDZyKx2IPWDkscVdodwQpDCs4So,11700
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/__pycache__/__init__.cpython-312.pyc,,
blib2to3/__pycache__/pygram.cpython-312.pyc,,
blib2to3/__pycache__/pytree.cpython-312.pyc,,
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/__pycache__/__init__.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/conv.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/driver.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/grammar.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/literals.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/parse.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/pgen.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/token.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/tokenize.cpython-312.pyc,,
blib2to3/pgen2/conv.cpython-312-darwin.so,sha256=BsyHjOAzzz9ift5JwP-Ej66Rjn8dbFtaP4q90Trl-Ic,50136
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.cpython-312-darwin.so,sha256=gCMlbkNED5ef4BRjMseZP6iHka1E_-2VrpVupmYH_uc,50144
blib2to3/pgen2/driver.py,sha256=hvycxhuy9IwXGs21qVKigEo74QGV_M1ZIPB9mFXjN58,10832
blib2to3/pgen2/grammar.cpython-312-darwin.so,sha256=PdPRg5kv2lQMacm7VZMIecZVuYSwo_06tUW-npxPC4s,50144
blib2to3/pgen2/grammar.py,sha256=6-wFBNM1I2rsnzfDfWeRNjfNYh4Raj_fb8cZX_FByUU,6865
blib2to3/pgen2/literals.cpython-312-darwin.so,sha256=M_FynGZjdnCqNHiqXf_CtgVqxeyhRqhvHUXIi3We_UI,50144
blib2to3/pgen2/literals.py,sha256=_LyRryELzqarFkW3OAEZzZ-yppCTm9g0mjqqQ2XygKE,1614
blib2to3/pgen2/parse.cpython-312-darwin.so,sha256=hwHImbylaWpatVeawdHvGBCx1qekhP6xsw_HmffU7cc,50136
blib2to3/pgen2/parse.py,sha256=mIGZ9BxV_G7jAjcwWefKomNGB8CfUPXqjZKQxH_fuuc,15658
blib2to3/pgen2/pgen.cpython-312-darwin.so,sha256=3KIgtGphGFBcpwxXAtx1_PBqbnjOZ3wuiaN_pqqgmPw,50136
blib2to3/pgen2/pgen.py,sha256=iQH8W999TKUT5AhuOpW38ZynwSACkVNV-I6z8kyQozY,15428
blib2to3/pgen2/token.cpython-312-darwin.so,sha256=D53r1txhsYKB-MYasB-U8OT8ne8sdR6b6jBB-3DCgF4,50136
blib2to3/pgen2/token.py,sha256=Xubq2lWE3gQ7PdUikcTL6_AWDk1OWhyw_Eb9xue4XJU,1899
blib2to3/pgen2/tokenize.cpython-312-darwin.so,sha256=C_VLXkg8NaT6cqFoQ_fIIYWWzJaB6wRBRvZlARgLTJk,50144
blib2to3/pgen2/tokenize.py,sha256=gnep0kccRh-kMUj13jWahGyuDm1DAizpJ89YFkTzyKQ,41353
blib2to3/pygram.cpython-312-darwin.so,sha256=J_Tv7jcdfymn2VBAoxZ1foaOLn2moz9JMVyu7MXtRVE,50144
blib2to3/pygram.py,sha256=l2qw7mw8I533KGWAXUFCXPGCN5F66hvYg86g-EA9GEg,4915
blib2to3/pytree.cpython-312-darwin.so,sha256=9_eDK3qZV5PqyI3vYVnKxOi8VXp0lT-rr9DjBe0PwN8,50144
blib2to3/pytree.py,sha256=mYKS_Py2zDhT815ZvlDy9kkRQ6PanRJIhR6R6AiBBIk,32666
