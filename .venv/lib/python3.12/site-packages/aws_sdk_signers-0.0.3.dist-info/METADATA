Metadata-Version: 2.4
Name: aws-sdk-signers
Version: 0.0.3
Summary: Standalone HTTP Request Signers for Amazon Web Services
Author: Amazon Web Services
License: Apache License 2.0
License-File: NOTICE
Keywords: amazon,aws,http,sdk,signing,sigv4
Classifier: Development Status :: 2 - Pre-Alpha
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.12
Provides-Extra: test
Requires-Dist: freezegun; extra == 'test'
Requires-Dist: mypy; extra == 'test'
Requires-Dist: pytest; extra == 'test'
Requires-Dist: pytest-asyncio; extra == 'test'
Requires-Dist: ruff; extra == 'test'
Description-Content-Type: text/markdown

## AWS SDK Signers for Python

AWS SDK Signers for Python provides stand-alone signing functionality. This enables users to
create standardized request signatures (currently only SigV4) and apply them to
common HTTP utilities like AIOHTTP, Curl, Postman, Requests and urllib3.

This project is currently in an **Alpha** phase of development. There likely
will be breakages and redesigns between minor patch versions as we collect
user feedback. We strongly recommend pinning to a minor version and reviewing
the changelog carefully before upgrading.

## Getting Started

Currently, the `aws-sdk-signers` module provides two high level signers,
`AsyncSigV4Signer` and `SigV4Signer`.

Both of these signers takes three inputs to their primary `sign` method.

* A [**SigV4SigningProperties**](https://github.com/smithy-lang/smithy-python/blob/3d205be8ece1c5f4c992a29ce9757c5562e59322/packages/aws-sdk-signers/src/aws_sdk_signers/signers.py#L43-L49) object defining:
  * The service for the request,
  * The intended AWS region (e.g. us-west-2),
  * An optional date that will be auto-populated with the current time if not supplied,
  * An optional boolean, payload_signing_enabled to toggle payload signing. True by default.
  * An optional boolean, content_checksum_enabled, to include the x-amz-content-sha256 header. True by default.
  * An optional boolean, uri_encode_path, to toggle double-encoding the URI path. True by default.
* An [**AWSRequest**](https://github.com/smithy-lang/smithy-python/blob/3d205be8ece1c5f4c992a29ce9757c5562e59322/packages/aws-sdk-signers/src/aws_sdk_signers/_http.py#L335), similar to the [AWSRequest object](https://github.com/boto/botocore/blob/7d197f9e1fe903ba3badee62a1ecac916ba2cfb5/botocore/awsrequest.py#L433) from boto3 or the [Request object](https://requests.readthedocs.io/en/latest/api/#requests.Request) from Requests.
* An [**AWSCredentialIdentity**](https://github.com/smithy-lang/smithy-python/blob/3d205be8ece1c5f4c992a29ce9757c5562e59322/packages/aws-sdk-signers/src/aws_sdk_signers/_identity.py#L11), a dataclass holding standard AWS credential information.

## License

This project is licensed under the Apache-2.0 License.
