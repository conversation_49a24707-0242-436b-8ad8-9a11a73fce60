# Code generated by smithy-python-codegen DO NOT EDIT.

import asyncio
from asyncio import Future, iscoroutine, sleep
from copy import copy, deepcopy
from dataclasses import replace
import logging
import re
from typing import Any, Awaitable, Callable, cast

from aws_sdk_signers import AsyncEventSigner
from smithy_aws_event_stream.aio import AWSEventPublisher, AWSEventReceiver
from smithy_core import URI
from smithy_core.aio.eventstream import (
    DuplexEventStream,
    InputEventStream,
    OutputEventStream,
)
from smithy_core.aio.interfaces.eventstream import EventReceiver
from smithy_core.aio.types import AsyncBytesReader
from smithy_core.deserializers import DeserializeableShape, ShapeDeserializer
from smithy_core.endpoints import EndpointResolverParams
from smithy_core.exceptions import SmithyRetryException
from smithy_core.interceptors import (
    InputContext,
    Interceptor,
    InterceptorChain,
    OutputContext,
    RequestContext,
    ResponseContext,
)
from smithy_core.interfaces.exceptions import <PERSON><PERSON>ault
from smithy_core.interfaces.identity import Identity
from smithy_core.interfaces.retries import RetryErrorInfo, RetryErrorType
from smithy_core.schemas import APIOperation
from smithy_core.serializers import SerializeableShape
from smithy_core.types import PropertyKey, TimestampFormat, TypedProperties
from smithy_http.aio.interfaces import HTTPRequest, HTTPResponse
from smithy_http.aio.interfaces.auth import HTTPAuthOption, HTTPSigner
from smithy_http.interfaces import HTTPRequestConfiguration
from smithy_http.plugins import user_agent_plugin
from smithy_json import JSONCodec

from .auth import HTTPAuthParams
from .config import Config, Plugin
from .deserialize import (
    _deserialize_apply_guardrail,
    _deserialize_converse,
    _deserialize_converse_stream,
    _deserialize_get_async_invoke,
    _deserialize_invoke_model,
    _deserialize_invoke_model_with_bidirectional_stream,
    _deserialize_invoke_model_with_response_stream,
    _deserialize_list_async_invokes,
    _deserialize_start_async_invoke,
)
from .models import (
    APPLY_GUARDRAIL,
    ApplyGuardrailInput,
    ApplyGuardrailOutput,
    CONVERSE,
    CONVERSE_STREAM,
    ConverseInput,
    ConverseOperationOutput,
    ConverseStreamInput,
    ConverseStreamOperationOutput,
    ConverseStreamOutput,
    GET_ASYNC_INVOKE,
    GetAsyncInvokeInput,
    GetAsyncInvokeOutput,
    INVOKE_MODEL,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM,
    INVOKE_MODEL_WITH_RESPONSE_STREAM,
    InvokeModelInput,
    InvokeModelOutput,
    InvokeModelWithBidirectionalStreamInput,
    InvokeModelWithBidirectionalStreamOperationInput,
    InvokeModelWithBidirectionalStreamOperationOutput,
    InvokeModelWithBidirectionalStreamOutput,
    InvokeModelWithResponseStreamInput,
    InvokeModelWithResponseStreamOutput,
    LIST_ASYNC_INVOKES,
    ListAsyncInvokesInput,
    ListAsyncInvokesOutput,
    ResponseStream,
    START_ASYNC_INVOKE,
    ServiceError,
    StartAsyncInvokeInput,
    StartAsyncInvokeOutput,
    _ConverseStreamOutputDeserializer,
    _InvokeModelWithBidirectionalStreamOutputDeserializer,
    _ResponseStreamDeserializer,
)
from .serialize import (
    _serialize_apply_guardrail,
    _serialize_converse,
    _serialize_converse_stream,
    _serialize_get_async_invoke,
    _serialize_invoke_model,
    _serialize_invoke_model_with_bidirectional_stream,
    _serialize_invoke_model_with_response_stream,
    _serialize_list_async_invokes,
    _serialize_start_async_invoke,
)
from .user_agent import aws_user_agent_plugin


logger = logging.getLogger(__name__)


class BedrockRuntimeClient:
    """
    Describes the API operations for running inference using Amazon Bedrock models.

    :param config: Optional configuration for the client. Here you can set things like the
        endpoint for HTTP services or auth credentials.

    :param plugins: A list of callables that modify the configuration dynamically. These
        can be used to set defaults, for example.
    """

    def __init__(
        self, config: Config | None = None, plugins: list[Plugin] | None = None
    ):
        self._config = config or Config()

        client_plugins: list[Plugin] = [
            aws_user_agent_plugin,
            user_agent_plugin,
        ]
        if plugins:
            client_plugins.extend(plugins)

        for plugin in client_plugins:
            plugin(self._config)

    async def apply_guardrail(
        self, input: ApplyGuardrailInput, plugins: list[Plugin] | None = None
    ) -> ApplyGuardrailOutput:
        """
        The action to apply a guardrail.

        For troubleshooting some of the common errors you might encounter when using the
        ``ApplyGuardrail`` API, see `Troubleshooting Amazon Bedrock API Error Codes <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html>`_
        in the Amazon Bedrock User Guide

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_apply_guardrail,
            deserialize=_deserialize_apply_guardrail,
            config=self._config,
            operation=APPLY_GUARDRAIL,
        )

    async def converse(
        self, input: ConverseInput, plugins: list[Plugin] | None = None
    ) -> ConverseOperationOutput:
        """
        Sends messages to the specified Amazon Bedrock model. ``Converse`` provides a
        consistent interface that works with all models that support messages. This
        allows you to write code once and use it with different models. If a model has
        unique inference parameters, you can also pass those unique parameters to the
        model.

        Amazon Bedrock doesn't store any text, images, or documents that you provide as
        content. The data is only used to generate the response.

        You can submit a prompt by including it in the ``messages`` field, specifying
        the ``modelId`` of a foundation model or inference profile to run inference on
        it, and including any other fields that are relevant to your use case.

        You can also submit a prompt from Prompt management by specifying the ARN of the
        prompt version and including a map of variables to values in the
        ``promptVariables`` field. You can append more messages to the prompt by using the ``messages`` field. If you use a prompt from Prompt management, you can't include the following fields in the request: ``additionalModelRequestFields``, ``inferenceConfig``, ``system``, or ``toolConfig``. Instead, these fields must be defined through Prompt management. For more information, see `Use a prompt from Prompt management <https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-use.html>`_
        .

        For information about the Converse API, see *Use the Converse API* in the
        *Amazon Bedrock User Guide*. To use a guardrail, see *Use a guardrail with the
        Converse API* in the *Amazon Bedrock User Guide*. To use a tool with a model,
        see *Tool use (Function calling)* in the *Amazon Bedrock User Guide*

        For example code, see *Converse API examples* in the *Amazon Bedrock User
        Guide*.

        This operation requires permission for the ``bedrock:InvokeModel`` action.

        .. important::
            To deny all inference access to resources that you specify in the modelId field,
            you need to deny access to the ``bedrock:InvokeModel`` and ``bedrock:InvokeModelWithResponseStream`` actions. Doing this also denies access to the resource through the base inference actions (`InvokeModel <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_InvokeModel.html>`_
            and `InvokeModelWithResponseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_InvokeModelWithResponseStream.html>`_).
            For more information see `Deny access for inference on specific models <https://docs.aws.amazon.com/bedrock/latest/userguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-deny-inference>`_
            .

        For troubleshooting some of the common errors you might encounter when using the
        ``Converse`` API, see `Troubleshooting Amazon Bedrock API Error Codes <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html>`_
        in the Amazon Bedrock User Guide

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_converse,
            deserialize=_deserialize_converse,
            config=self._config,
            operation=CONVERSE,
        )

    async def converse_stream(
        self, input: ConverseStreamInput, plugins: list[Plugin] | None = None
    ) -> OutputEventStream[ConverseStreamOutput, ConverseStreamOperationOutput]:
        """
        Sends messages to the specified Amazon Bedrock model and returns the response in
        a stream. ``ConverseStream`` provides a consistent API that works with all
        Amazon Bedrock models that support messages. This allows you to write code once
        and use it with different models. Should a model have unique inference
        parameters, you can also pass those unique parameters to the model.

        To find out if a model supports streaming, call `GetFoundationModel <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetFoundationModel.html>`_
        and check the ``responseStreamingSupported`` field in the response.

        .. note::
            The CLI doesn't support streaming operations in Amazon Bedrock, including
            ``ConverseStream``.

        Amazon Bedrock doesn't store any text, images, or documents that you provide as
        content. The data is only used to generate the response.

        You can submit a prompt by including it in the ``messages`` field, specifying
        the ``modelId`` of a foundation model or inference profile to run inference on
        it, and including any other fields that are relevant to your use case.

        You can also submit a prompt from Prompt management by specifying the ARN of the
        prompt version and including a map of variables to values in the
        ``promptVariables`` field. You can append more messages to the prompt by using the ``messages`` field. If you use a prompt from Prompt management, you can't include the following fields in the request: ``additionalModelRequestFields``, ``inferenceConfig``, ``system``, or ``toolConfig``. Instead, these fields must be defined through Prompt management. For more information, see `Use a prompt from Prompt management <https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-use.html>`_
        .

        For information about the Converse API, see *Use the Converse API* in the
        *Amazon Bedrock User Guide*. To use a guardrail, see *Use a guardrail with the
        Converse API* in the *Amazon Bedrock User Guide*. To use a tool with a model,
        see *Tool use (Function calling)* in the *Amazon Bedrock User Guide*

        For example code, see *Conversation streaming example* in the *Amazon Bedrock
        User Guide*.

        This operation requires permission for the
        ``bedrock:InvokeModelWithResponseStream`` action.

        .. important::
            To deny all inference access to resources that you specify in the modelId field,
            you need to deny access to the ``bedrock:InvokeModel`` and ``bedrock:InvokeModelWithResponseStream`` actions. Doing this also denies access to the resource through the base inference actions (`InvokeModel <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_InvokeModel.html>`_
            and `InvokeModelWithResponseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_InvokeModelWithResponseStream.html>`_).
            For more information see `Deny access for inference on specific models <https://docs.aws.amazon.com/bedrock/latest/userguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-deny-inference>`_
            .

        For troubleshooting some of the common errors you might encounter when using the
        ``ConverseStream`` API, see `Troubleshooting Amazon Bedrock API Error Codes <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html>`_
        in the Amazon Bedrock User Guide

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._output_stream(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_converse_stream,
            deserialize=_deserialize_converse_stream,
            config=self._config,
            operation=CONVERSE_STREAM,
            event_deserializer=_ConverseStreamOutputDeserializer().deserialize,
        )  # type: ignore

    async def get_async_invoke(
        self, input: GetAsyncInvokeInput, plugins: list[Plugin] | None = None
    ) -> GetAsyncInvokeOutput:
        """
        Retrieve information about an asynchronous invocation.

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_get_async_invoke,
            deserialize=_deserialize_get_async_invoke,
            config=self._config,
            operation=GET_ASYNC_INVOKE,
        )

    async def invoke_model(
        self, input: InvokeModelInput, plugins: list[Plugin] | None = None
    ) -> InvokeModelOutput:
        """
        Invokes the specified Amazon Bedrock model to run inference using the prompt and
        inference parameters provided in the request body. You use model inference to
        generate text, images, and embeddings.

        For example code, see *Invoke model code examples* in the *Amazon Bedrock User
        Guide*.

        This operation requires permission for the ``bedrock:InvokeModel`` action.

        .. important::
            To deny all inference access to resources that you specify in the modelId field,
            you need to deny access to the ``bedrock:InvokeModel`` and ``bedrock:InvokeModelWithResponseStream`` actions. Doing this also denies access to the resource through the Converse API actions (`Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
            and `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_).
            For more information see `Deny access for inference on specific models <https://docs.aws.amazon.com/bedrock/latest/userguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-deny-inference>`_
            .

        For troubleshooting some of the common errors you might encounter when using the
        ``InvokeModel`` API, see `Troubleshooting Amazon Bedrock API Error Codes <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html>`_
        in the Amazon Bedrock User Guide

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_invoke_model,
            deserialize=_deserialize_invoke_model,
            config=self._config,
            operation=INVOKE_MODEL,
        )

    async def invoke_model_with_bidirectional_stream(
        self,
        input: InvokeModelWithBidirectionalStreamOperationInput,
        plugins: list[Plugin] | None = None,
    ) -> DuplexEventStream[
        InvokeModelWithBidirectionalStreamInput,
        InvokeModelWithBidirectionalStreamOutput,
        InvokeModelWithBidirectionalStreamOperationOutput,
    ]:
        """
        Invoke the specified Amazon Bedrock model to run inference using the
        bidirectional stream. The response is returned in a stream that remains open for
        8 minutes. A single session can contain multiple prompts and responses from the
        model. The prompts to the model are provided as audio files and the model's
        responses are spoken back to the user and transcribed.

        It is possible for users to interrupt the model's response with a new prompt,
        which will halt the response speech. The model will retain contextual awareness
        of the conversation while pivoting to respond to the new prompt.

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._duplex_stream(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_invoke_model_with_bidirectional_stream,
            deserialize=_deserialize_invoke_model_with_bidirectional_stream,
            config=self._config,
            operation=INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM,
            event_deserializer=_InvokeModelWithBidirectionalStreamOutputDeserializer().deserialize,
        )  # type: ignore

    async def invoke_model_with_response_stream(
        self,
        input: InvokeModelWithResponseStreamInput,
        plugins: list[Plugin] | None = None,
    ) -> OutputEventStream[ResponseStream, InvokeModelWithResponseStreamOutput]:
        """
        Invoke the specified Amazon Bedrock model to run inference using the prompt and
        inference parameters provided in the request body. The response is returned in a
        stream.

        To see if a model supports streaming, call `GetFoundationModel <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetFoundationModel.html>`_
        and check the ``responseStreamingSupported`` field in the response.

        .. note::
            The CLI doesn't support streaming operations in Amazon Bedrock, including
            ``InvokeModelWithResponseStream``.

        For example code, see *Invoke model with streaming code example* in the *Amazon
        Bedrock User Guide*.

        This operation requires permissions to perform the
        ``bedrock:InvokeModelWithResponseStream`` action.

        .. important::
            To deny all inference access to resources that you specify in the modelId field,
            you need to deny access to the ``bedrock:InvokeModel`` and ``bedrock:InvokeModelWithResponseStream`` actions. Doing this also denies access to the resource through the Converse API actions (`Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
            and `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_).
            For more information see `Deny access for inference on specific models <https://docs.aws.amazon.com/bedrock/latest/userguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-deny-inference>`_
            .

        For troubleshooting some of the common errors you might encounter when using the
        ``InvokeModelWithResponseStream`` API, see `Troubleshooting Amazon Bedrock API Error Codes <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html>`_
        in the Amazon Bedrock User Guide

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._output_stream(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_invoke_model_with_response_stream,
            deserialize=_deserialize_invoke_model_with_response_stream,
            config=self._config,
            operation=INVOKE_MODEL_WITH_RESPONSE_STREAM,
            event_deserializer=_ResponseStreamDeserializer().deserialize,
        )  # type: ignore

    async def list_async_invokes(
        self, input: ListAsyncInvokesInput, plugins: list[Plugin] | None = None
    ) -> ListAsyncInvokesOutput:
        """
        Lists asynchronous invocations.

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_list_async_invokes,
            deserialize=_deserialize_list_async_invokes,
            config=self._config,
            operation=LIST_ASYNC_INVOKES,
        )

    async def start_async_invoke(
        self, input: StartAsyncInvokeInput, plugins: list[Plugin] | None = None
    ) -> StartAsyncInvokeOutput:
        """
        Starts an asynchronous invocation.

        This operation requires permission for the ``bedrock:InvokeModel`` action.

        .. important::
            To deny all inference access to resources that you specify in the modelId field,
            you need to deny access to the ``bedrock:InvokeModel`` and ``bedrock:InvokeModelWithResponseStream`` actions. Doing this also denies access to the resource through the Converse API actions (`Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
            and `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_).
            For more information see `Deny access for inference on specific models <https://docs.aws.amazon.com/bedrock/latest/userguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-deny-inference>`_
            .

        :param input: The operation's input.

        :param plugins: A list of callables that modify the configuration dynamically.
            Changes made by these plugins only apply for the duration of the operation
            execution and will not affect any other operation invocations.

        """
        operation_plugins: list[Plugin] = []
        if plugins:
            operation_plugins.extend(plugins)

        return await self._execute_operation(
            input=input,
            plugins=operation_plugins,
            serialize=_serialize_start_async_invoke,
            deserialize=_deserialize_start_async_invoke,
            config=self._config,
            operation=START_ASYNC_INVOKE,
        )

    def _classify_error(
        self,
        *,
        error: Exception,
        context: ResponseContext[Any, HTTPRequest, HTTPResponse | None],
    ) -> RetryErrorInfo:
        logger.debug("Classifying error: %s", error)

        if not isinstance(error, HasFault) and not context.transport_response:
            return RetryErrorInfo(error_type=RetryErrorType.TRANSIENT)

        if context.transport_response:
            if context.transport_response.status in [429, 503]:
                retry_after = None
                retry_header = context.transport_response.fields["retry-after"]
                if retry_header and retry_header.values:
                    retry_after = float(retry_header.values[0])
                return RetryErrorInfo(
                    error_type=RetryErrorType.THROTTLING, retry_after_hint=retry_after
                )

            if context.transport_response.status >= 500:
                return RetryErrorInfo(error_type=RetryErrorType.SERVER_ERROR)

        error_type = RetryErrorType.CLIENT_ERROR
        if isinstance(error, HasFault) and error.fault == "server":
            error_type = RetryErrorType.SERVER_ERROR

        return RetryErrorInfo(error_type=error_type)

    async def _input_stream[Input: SerializeableShape, Output: DeserializeableShape](
        self,
        input: Input,
        plugins: list[Plugin],
        serialize: Callable[[Input, Config], Awaitable[HTTPRequest]],
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        config: Config,
        operation: APIOperation[Input, Output],
    ) -> Any:
        request_future = Future[RequestContext[Any, HTTPRequest]]()
        awaitable_output = asyncio.create_task(
            self._execute_operation(
                input,
                plugins,
                serialize,
                deserialize,
                config,
                operation,
                request_future=request_future,
            )
        )
        request_context = await request_future
        # TODO - Move this out of the RestJSON generator
        ctx = request_context
        signer_properties = ctx.properties.get("signer_properties")  # type: ignore
        identity = ctx.properties.get("identity")  # type: ignore
        signature = ctx.properties.get("signature")  # type: ignore
        signer = AsyncEventSigner(
            signing_properties=signer_properties,  # type: ignore
            identity=identity,  # type: ignore
            initial_signature=signature,  # type: ignore
        )
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        publisher = AWSEventPublisher[Any](
            payload_codec=codec,
            async_writer=request_context.transport_request.body,  # type: ignore
            signer=signer,  # type: ignore
        )

        return InputEventStream[Any, Any](
            input_stream=publisher,
            output_future=awaitable_output,
        )

    async def _output_stream[Input: SerializeableShape, Output: DeserializeableShape](
        self,
        input: Input,
        plugins: list[Plugin],
        serialize: Callable[[Input, Config], Awaitable[HTTPRequest]],
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        config: Config,
        operation: APIOperation[Input, Output],
        event_deserializer: Callable[[ShapeDeserializer], Any],
    ) -> Any:
        response_future = Future[HTTPResponse]()
        output = await self._execute_operation(
            input,
            plugins,
            serialize,
            deserialize,
            config,
            operation,
            response_future=response_future,
        )
        transport_response = await response_future
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        receiver = AWSEventReceiver(
            payload_codec=codec,
            source=AsyncBytesReader(
                transport_response.body  # type: ignore
            ),
            deserializer=event_deserializer,  # type: ignore
        )

        return OutputEventStream[Any, Any](output_stream=receiver, output=output)

    async def _duplex_stream[Input: SerializeableShape, Output: DeserializeableShape](
        self,
        input: Input,
        plugins: list[Plugin],
        serialize: Callable[[Input, Config], Awaitable[HTTPRequest]],
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        config: Config,
        operation: APIOperation[Input, Output],
        event_deserializer: Callable[[ShapeDeserializer], Any],
    ) -> Any:
        request_future = Future[RequestContext[Any, HTTPRequest]]()
        response_future = Future[HTTPResponse]()
        awaitable_output = asyncio.create_task(
            self._execute_operation(
                input,
                plugins,
                serialize,
                deserialize,
                config,
                operation,
                request_future=request_future,
                response_future=response_future,
            )
        )
        request_context = await request_future
        # TODO - Move this out of the RestJSON generator
        ctx = request_context
        signer_properties = ctx.properties.get("signer_properties")  # type: ignore
        identity = ctx.properties.get("identity")  # type: ignore
        signature = ctx.properties.get("signature")  # type: ignore
        signer = AsyncEventSigner(
            signing_properties=signer_properties,  # type: ignore
            identity=identity,  # type: ignore
            initial_signature=signature,  # type: ignore
        )
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        publisher = AWSEventPublisher[Any](
            payload_codec=codec,
            async_writer=request_context.transport_request.body,  # type: ignore
            signer=signer,  # type: ignore
        )

        output_future = asyncio.create_task(
            self._wrap_duplex_output(
                response_future, awaitable_output, config, operation, event_deserializer
            )
        )
        return DuplexEventStream[Any, Any, Any](
            input_stream=publisher,
            output_future=output_future,
        )

    async def _wrap_duplex_output[
        Input: SerializeableShape,
        Output: DeserializeableShape,
    ](
        self,
        response_future: Future[HTTPResponse],
        awaitable_output: Future[Any],
        config: Config,
        operation: APIOperation[Input, Output],
        event_deserializer: Callable[[ShapeDeserializer], Any],
    ) -> tuple[Any, EventReceiver[Any]]:
        transport_response = await response_future
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        receiver = AWSEventReceiver(
            payload_codec=codec,
            source=AsyncBytesReader(
                transport_response.body  # type: ignore
            ),
            deserializer=event_deserializer,  # type: ignore
        )

        return await awaitable_output, receiver

    async def _execute_operation[
        Input: SerializeableShape,
        Output: DeserializeableShape,
    ](
        self,
        input: Input,
        plugins: list[Plugin],
        serialize: Callable[[Input, Config], Awaitable[HTTPRequest]],
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        config: Config,
        operation: APIOperation[Input, Output],
        request_future: Future[RequestContext[Any, HTTPRequest]] | None = None,
        response_future: Future[HTTPResponse] | None = None,
    ) -> Output:
        try:
            return await self._handle_execution(
                input,
                plugins,
                serialize,
                deserialize,
                config,
                operation,
                request_future,
                response_future,
            )
        except Exception as e:
            if request_future is not None and not request_future.done():
                request_future.set_exception(ServiceError(e))
            if response_future is not None and not response_future.done():
                response_future.set_exception(ServiceError(e))

            # Make sure every exception that we throw is an instance of ServiceError so
            # customers can reliably catch everything we throw.
            if not isinstance(e, ServiceError):
                raise ServiceError(e) from e
            raise

    async def _handle_execution[
        Input: SerializeableShape,
        Output: DeserializeableShape,
    ](
        self,
        input: Input,
        plugins: list[Plugin],
        serialize: Callable[[Input, Config], Awaitable[HTTPRequest]],
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        config: Config,
        operation: APIOperation[Input, Output],
        request_future: Future[RequestContext[Any, HTTPRequest]] | None,
        response_future: Future[HTTPResponse] | None,
    ) -> Output:
        operation_name = operation.schema.id.name
        logger.debug(
            'Making request for operation "%s" with parameters: %s',
            operation_name,
            input,
        )
        config = deepcopy(config)
        for plugin in plugins:
            plugin(config)

        input_context = InputContext(
            request=input, properties=TypedProperties({"config": config})
        )
        transport_request: HTTPRequest | None = None
        output_context: (
            OutputContext[Input, Output, HTTPRequest | None, HTTPResponse | None] | None
        ) = None

        client_interceptors = cast(
            list[Interceptor[Input, Output, HTTPRequest, HTTPResponse]],
            list(config.interceptors),
        )
        interceptor_chain = InterceptorChain(client_interceptors)

        try:
            # Step 1: Invoke read_before_execution
            interceptor_chain.read_before_execution(input_context)

            # Step 2: Invoke the modify_before_serialization hooks
            input_context = replace(
                input_context,
                request=interceptor_chain.modify_before_serialization(input_context),
            )

            # Step 3: Invoke the read_before_serialization hooks
            interceptor_chain.read_before_serialization(input_context)

            # Step 4: Serialize the request
            logger.debug("Serializing request for: %s", input_context.request)
            transport_request = await serialize(input_context.request, config)
            request_context = RequestContext(
                request=input_context.request,
                transport_request=transport_request,
                properties=input_context.properties,
            )
            logger.debug(
                "Serialization complete. Transport request: %s",
                request_context.transport_request,
            )

            # Step 5: Invoke read_after_serialization
            interceptor_chain.read_after_serialization(request_context)

            # Step 6: Invoke modify_before_retry_loop
            request_context = replace(
                request_context,
                transport_request=interceptor_chain.modify_before_retry_loop(
                    request_context
                ),
            )

            # Step 7: Acquire the retry token.
            retry_strategy = config.retry_strategy
            retry_token = retry_strategy.acquire_initial_retry_token()

            while True:
                # Make an attempt
                output_context = await self._handle_attempt(
                    deserialize,
                    interceptor_chain,
                    replace(
                        request_context,
                        transport_request=copy(request_context.transport_request),
                    ),
                    config,
                    operation,
                    request_future,
                )

                if isinstance(output_context.response, Exception):
                    # Step 7u: Reacquire retry token if the attempt failed
                    try:
                        retry_token = retry_strategy.refresh_retry_token_for_retry(
                            token_to_renew=retry_token,
                            error_info=self._classify_error(
                                error=output_context.response,
                                context=output_context,
                            ),
                        )
                    except SmithyRetryException:
                        raise output_context.response
                    logger.debug(
                        "Retry needed. Attempting request #%s in %.4f seconds.",
                        retry_token.retry_count + 1,
                        retry_token.retry_delay,
                    )
                    await sleep(retry_token.retry_delay)
                    current_body = output_context.transport_request.body
                    if (seek := getattr(current_body, "seek", None)) is not None:
                        if iscoroutine((result := seek(0))):
                            await result
                else:
                    # Step 8: Invoke record_success
                    retry_strategy.record_success(token=retry_token)
                    if response_future is not None:
                        response_future.set_result(
                            output_context.transport_response  # type: ignore
                        )
                    break
        except Exception as e:
            if output_context is not None:
                logger.exception(
                    "Exception occurred while handling: %s", output_context.response
                )
                output_context = replace(output_context, response=e)
            else:
                output_context = OutputContext(
                    request=input_context.request,
                    response=e,
                    transport_request=transport_request,
                    transport_response=None,
                    properties=input_context.properties,
                )

        return await self._finalize_execution(interceptor_chain, output_context)

    async def _handle_attempt[Input: SerializeableShape, Output: DeserializeableShape](
        self,
        deserialize: Callable[[HTTPResponse, Config], Awaitable[Output]],
        interceptor: Interceptor[Input, Output, HTTPRequest, HTTPResponse],
        context: RequestContext[Input, HTTPRequest],
        config: Config,
        operation: APIOperation[Input, Output],
        request_future: Future[RequestContext[Input, HTTPRequest]] | None,
    ) -> OutputContext[Input, Output, HTTPRequest, HTTPResponse | None]:
        transport_response: HTTPResponse | None = None
        try:
            # Step 7a: Invoke read_before_attempt
            interceptor.read_before_attempt(context)

            # Step 7b: Invoke service_auth_scheme_resolver.resolve_auth_scheme
            auth_parameters: HTTPAuthParams = HTTPAuthParams(
                operation=operation.schema.id.name,
                region=config.region,
            )

            auth_options = config.http_auth_scheme_resolver.resolve_auth_scheme(
                auth_parameters=auth_parameters
            )
            auth_option: HTTPAuthOption | None = None
            for option in auth_options:
                if option.scheme_id in config.http_auth_schemes:
                    auth_option = option
                    break

            signer: HTTPSigner[Any, Any] | None = None
            identity: Identity | None = None

            if auth_option:
                auth_scheme = config.http_auth_schemes[auth_option.scheme_id]

                # Step 7c: Invoke auth_scheme.identity_resolver
                identity_resolver = auth_scheme.identity_resolver(config=config)

                # Step 7d: Invoke auth_scheme.signer
                signer = auth_scheme.signer

                # Step 7e: Invoke identity_resolver.get_identity
                identity = await identity_resolver.get_identity(
                    identity_properties=auth_option.identity_properties
                )

            # Step 7f: Invoke endpoint_resolver.resolve_endpoint
            endpoint_resolver_parameters = EndpointResolverParams(
                operation=operation, input=context.request, context=context.properties
            )
            logger.debug(
                "Calling endpoint resolver with parameters: %s",
                endpoint_resolver_parameters,
            )
            endpoint = await config.endpoint_resolver.resolve_endpoint(
                endpoint_resolver_parameters
            )
            logger.debug("Endpoint resolver result: %s", endpoint)
            if not endpoint.uri.path:
                path = ""
            elif endpoint.uri.path.endswith("/"):
                path = endpoint.uri.path[:-1]
            else:
                path = endpoint.uri.path
            if context.transport_request.destination.path:
                path += context.transport_request.destination.path
            context.transport_request.destination = URI(
                scheme=endpoint.uri.scheme,
                host=context.transport_request.destination.host + endpoint.uri.host,
                path=path,
                port=endpoint.uri.port,
                query=context.transport_request.destination.query,
            )

            if (headers := endpoint.properties.get("headers")) is not None:
                context.transport_request.fields.extend(headers)

            # Step 7g: Invoke modify_before_signing
            context = replace(
                context, transport_request=interceptor.modify_before_signing(context)
            )

            # Step 7h: Invoke read_before_signing
            interceptor.read_before_signing(context)

            # Step 7i: sign the request
            if auth_option and signer:
                logger.debug("HTTP request to sign: %s", context.transport_request)
                logger.debug("Signer properties: %s", auth_option.signer_properties)
                context = replace(
                    context,
                    transport_request=await signer.sign(
                        http_request=context.transport_request,
                        identity=identity,
                        signing_properties=auth_option.signer_properties,
                    ),
                )
                logger.debug("Signed HTTP request: %s", context.transport_request)

                # TODO - Move this to separate resolution/population function
                fields = context.transport_request.fields
                auth_value = fields["Authorization"].as_string()  # type: ignore
                signature = re.split("Signature=", auth_value)[-1]  # type: ignore
                context.properties["signature"] = signature.encode("utf-8")

                identity_key: PropertyKey[Identity | None] = PropertyKey(
                    key="identity",
                    value_type=Identity | None,  # type: ignore
                )
                sp_key: PropertyKey[dict[str, Any]] = PropertyKey(
                    key="signer_properties",
                    value_type=dict[str, Any],  # type: ignore
                )
                context.properties[identity_key] = identity
                context.properties[sp_key] = auth_option.signer_properties

            # Step 7j: Invoke read_after_signing
            interceptor.read_after_signing(context)

            # Step 7k: Invoke modify_before_transmit
            context = replace(
                context, transport_request=interceptor.modify_before_transmit(context)
            )

            # Step 7l: Invoke read_before_transmit
            interceptor.read_before_transmit(context)

            # Step 7m: Invoke http_client.send
            request_config = config.http_request_config or HTTPRequestConfiguration()
            logger.debug("HTTP request config: %s", request_config)
            logger.debug("Sending HTTP request: %s", context.transport_request)

            if request_future is not None:
                response_task = asyncio.create_task(
                    config.http_client.send(
                        request=context.transport_request,
                        request_config=request_config,
                    )
                )
                request_future.set_result(context)
                transport_response = await response_task
            else:
                transport_response = await config.http_client.send(
                    request=context.transport_request,
                    request_config=request_config,
                )

            response_context = ResponseContext(
                request=context.request,
                transport_request=context.transport_request,
                transport_response=transport_response,
                properties=context.properties,
            )
            logger.debug(
                "Received HTTP response: %s", response_context.transport_response
            )

            # Step 7n: Invoke read_after_transmit
            interceptor.read_after_transmit(response_context)

            # Step 7o: Invoke modify_before_deserialization
            response_context = replace(
                response_context,
                transport_response=interceptor.modify_before_deserialization(
                    response_context
                ),
            )

            # Step 7p: Invoke read_before_deserialization
            interceptor.read_before_deserialization(response_context)

            # Step 7q: deserialize
            logger.debug(
                "Deserializing transport response: %s",
                response_context.transport_response,
            )
            output = await deserialize(response_context.transport_response, config)
            output_context = OutputContext(
                request=response_context.request,
                response=output,
                transport_request=response_context.transport_request,
                transport_response=response_context.transport_response,
                properties=response_context.properties,
            )
            logger.debug(
                "Deserialization complete. Response: %s", output_context.response
            )

            # Step 7r: Invoke read_after_deserialization
            interceptor.read_after_deserialization(output_context)
        except Exception as e:
            output_context: OutputContext[Input, Output, HTTPRequest, HTTPResponse] = (
                OutputContext(
                    request=context.request,
                    response=e,  # type: ignore
                    transport_request=context.transport_request,
                    transport_response=transport_response,
                    properties=context.properties,
                )
            )

        return await self._finalize_attempt(interceptor, output_context)

    async def _finalize_attempt[
        Input: SerializeableShape,
        Output: DeserializeableShape,
    ](
        self,
        interceptor: Interceptor[Input, Output, HTTPRequest, HTTPResponse],
        context: OutputContext[Input, Output, HTTPRequest, HTTPResponse | None],
    ) -> OutputContext[Input, Output, HTTPRequest, HTTPResponse | None]:
        # Step 7s: Invoke modify_before_attempt_completion
        try:
            context = replace(
                context, response=interceptor.modify_before_attempt_completion(context)
            )
        except Exception as e:
            logger.exception("Exception occurred while handling: %s", context.response)
            context = replace(context, response=e)

        # Step 7t: Invoke read_after_attempt
        try:
            interceptor.read_after_attempt(context)
        except Exception as e:
            context = replace(context, response=e)

        return context

    async def _finalize_execution[
        Input: SerializeableShape,
        Output: DeserializeableShape,
    ](
        self,
        interceptor: Interceptor[Input, Output, HTTPRequest, HTTPResponse],
        context: OutputContext[Input, Output, HTTPRequest | None, HTTPResponse | None],
    ) -> Output:
        try:
            # Step 9: Invoke modify_before_completion
            context = replace(
                context, response=interceptor.modify_before_completion(context)
            )

            # Step 10: Invoke trace_probe.dispatch_events
            try:
                pass
            except Exception as e:
                # log and ignore exceptions
                logger.exception(
                    "Exception occurred while dispatching trace events: %s", e
                )
                pass
        except Exception as e:
            logger.exception("Exception occurred while handling: %s", context.response)
            context = replace(context, response=e)

        # Step 11: Invoke read_after_execution
        try:
            interceptor.read_after_execution(context)
        except Exception as e:
            context = replace(context, response=e)

        # Step 12: Return / throw
        if isinstance(context.response, Exception):
            raise context.response

        # We may want to add some aspects of this context to the output types so we can
        # return it to the end-users.
        return context.response
