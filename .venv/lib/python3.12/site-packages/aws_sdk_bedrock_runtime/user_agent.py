# Code generated by smithy-python-codegen DO NOT EDIT.

from smithy_aws_core.interceptors.user_agent import UserAgentInterceptor

from . import __version__
from .config import Config


def aws_user_agent_plugin(config: Config):
    config.interceptors.append(
        UserAgentInterceptor(
            ua_suffix=config.user_agent_extra,
            ua_app_id=config.sdk_ua_app_id,
            sdk_version=__version__,
            service_id="Bedrock_Runtime",
        )
    )
