# Code generated by smithy-python-codegen DO NOT EDIT.

import json
from typing import Any

from smithy_core.documents import DocumentValue
from smithy_core.types import TimestampFormat
from smithy_http.aio.interfaces import HTTPResponse
from smithy_http.aio.restjson import parse_rest_json_error_info
from smithy_json import J<PERSON>NCodec

from .config import Config
from .models import (
    AccessDeniedException,
    ApiError,
    ApplyGuardrailOutput,
    ConflictException,
    ConverseOperationOutput,
    ConverseStreamOperationOutput,
    GetAsyncInvokeOutput,
    InternalServerException,
    InvokeModelOutput,
    InvokeModelWithBidirectionalStreamOperationOutput,
    InvokeModelWithResponseStreamOutput,
    ListAsyncInvokesOutput,
    ModelErrorException,
    ModelNotReadyException,
    ModelStreamErrorException,
    ModelTimeoutException,
    ResourceNotFoundException,
    ServiceQuotaExceededException,
    ServiceUnavailableException,
    StartAsyncInvokeOutput,
    ThrottlingException,
    UnknownApiError,
    ValidationException,
)


async def _deserialize_apply_guardrail(
    http_response: HTTPResponse, config: Config
) -> ApplyGuardrailOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_apply_guardrail(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ApplyGuardrailOutput.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ApplyGuardrailOutput(**kwargs)


async def _deserialize_error_apply_guardrail(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "servicequotaexceededexception":
            return await _deserialize_error_service_quota_exceeded_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_converse(
    http_response: HTTPResponse, config: Config
) -> ConverseOperationOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_converse(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ConverseOperationOutput.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ConverseOperationOutput(**kwargs)


async def _deserialize_error_converse(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "modelerrorexception":
            return await _deserialize_error_model_error_exception(
                http_response, config, parsed_body, message
            )

        case "modelnotreadyexception":
            return await _deserialize_error_model_not_ready_exception(
                http_response, config, parsed_body, message
            )

        case "modeltimeoutexception":
            return await _deserialize_error_model_timeout_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_converse_stream(
    http_response: HTTPResponse, config: Config
) -> ConverseStreamOperationOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_converse_stream(http_response, config)

    kwargs: dict[str, Any] = {}

    return ConverseStreamOperationOutput(**kwargs)


async def _deserialize_error_converse_stream(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "modelerrorexception":
            return await _deserialize_error_model_error_exception(
                http_response, config, parsed_body, message
            )

        case "modelnotreadyexception":
            return await _deserialize_error_model_not_ready_exception(
                http_response, config, parsed_body, message
            )

        case "modeltimeoutexception":
            return await _deserialize_error_model_timeout_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_get_async_invoke(
    http_response: HTTPResponse, config: Config
) -> GetAsyncInvokeOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_get_async_invoke(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = GetAsyncInvokeOutput.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return GetAsyncInvokeOutput(**kwargs)


async def _deserialize_error_get_async_invoke(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_invoke_model(
    http_response: HTTPResponse, config: Config
) -> InvokeModelOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_invoke_model(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        kwargs["body"] = body

    for fld in http_response.fields:
        for key, value in fld.as_tuples():
            _key_lowercase = key.lower()
            match _key_lowercase:
                case "content-type":
                    kwargs["content_type"] = value

                case "x-amzn-bedrock-performanceconfig-latency":
                    kwargs["performance_config_latency"] = value

                case _:
                    pass

    return InvokeModelOutput(**kwargs)


async def _deserialize_error_invoke_model(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "modelerrorexception":
            return await _deserialize_error_model_error_exception(
                http_response, config, parsed_body, message
            )

        case "modelnotreadyexception":
            return await _deserialize_error_model_not_ready_exception(
                http_response, config, parsed_body, message
            )

        case "modeltimeoutexception":
            return await _deserialize_error_model_timeout_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "servicequotaexceededexception":
            return await _deserialize_error_service_quota_exceeded_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_invoke_model_with_bidirectional_stream(
    http_response: HTTPResponse, config: Config
) -> InvokeModelWithBidirectionalStreamOperationOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_invoke_model_with_bidirectional_stream(
            http_response, config
        )

    kwargs: dict[str, Any] = {}

    return InvokeModelWithBidirectionalStreamOperationOutput(**kwargs)


async def _deserialize_error_invoke_model_with_bidirectional_stream(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "modelerrorexception":
            return await _deserialize_error_model_error_exception(
                http_response, config, parsed_body, message
            )

        case "modelnotreadyexception":
            return await _deserialize_error_model_not_ready_exception(
                http_response, config, parsed_body, message
            )

        case "modelstreamerrorexception":
            return await _deserialize_error_model_stream_error_exception(
                http_response, config, parsed_body, message
            )

        case "modeltimeoutexception":
            return await _deserialize_error_model_timeout_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "servicequotaexceededexception":
            return await _deserialize_error_service_quota_exceeded_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_invoke_model_with_response_stream(
    http_response: HTTPResponse, config: Config
) -> InvokeModelWithResponseStreamOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_invoke_model_with_response_stream(
            http_response, config
        )

    kwargs: dict[str, Any] = {}

    for fld in http_response.fields:
        for key, value in fld.as_tuples():
            _key_lowercase = key.lower()
            match _key_lowercase:
                case "x-amzn-bedrock-content-type":
                    kwargs["content_type"] = value

                case "x-amzn-bedrock-performanceconfig-latency":
                    kwargs["performance_config_latency"] = value

                case _:
                    pass

    return InvokeModelWithResponseStreamOutput(**kwargs)


async def _deserialize_error_invoke_model_with_response_stream(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "modelerrorexception":
            return await _deserialize_error_model_error_exception(
                http_response, config, parsed_body, message
            )

        case "modelnotreadyexception":
            return await _deserialize_error_model_not_ready_exception(
                http_response, config, parsed_body, message
            )

        case "modelstreamerrorexception":
            return await _deserialize_error_model_stream_error_exception(
                http_response, config, parsed_body, message
            )

        case "modeltimeoutexception":
            return await _deserialize_error_model_timeout_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "servicequotaexceededexception":
            return await _deserialize_error_service_quota_exceeded_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_list_async_invokes(
    http_response: HTTPResponse, config: Config
) -> ListAsyncInvokesOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_list_async_invokes(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ListAsyncInvokesOutput.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ListAsyncInvokesOutput(**kwargs)


async def _deserialize_error_list_async_invokes(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_start_async_invoke(
    http_response: HTTPResponse, config: Config
) -> StartAsyncInvokeOutput:
    if http_response.status != 200 and http_response.status >= 300:
        raise await _deserialize_error_start_async_invoke(http_response, config)

    kwargs: dict[str, Any] = {}

    body = await http_response.consume_body_async()
    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = StartAsyncInvokeOutput.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return StartAsyncInvokeOutput(**kwargs)


async def _deserialize_error_start_async_invoke(
    http_response: HTTPResponse, config: Config
) -> ApiError:
    code, message, parsed_body = await parse_rest_json_error_info(http_response)

    match code.lower():
        case "accessdeniedexception":
            return await _deserialize_error_access_denied_exception(
                http_response, config, parsed_body, message
            )

        case "conflictexception":
            return await _deserialize_error_conflict_exception(
                http_response, config, parsed_body, message
            )

        case "internalserverexception":
            return await _deserialize_error_internal_server_exception(
                http_response, config, parsed_body, message
            )

        case "resourcenotfoundexception":
            return await _deserialize_error_resource_not_found_exception(
                http_response, config, parsed_body, message
            )

        case "servicequotaexceededexception":
            return await _deserialize_error_service_quota_exceeded_exception(
                http_response, config, parsed_body, message
            )

        case "serviceunavailableexception":
            return await _deserialize_error_service_unavailable_exception(
                http_response, config, parsed_body, message
            )

        case "throttlingexception":
            return await _deserialize_error_throttling_exception(
                http_response, config, parsed_body, message
            )

        case "validationexception":
            return await _deserialize_error_validation_exception(
                http_response, config, parsed_body, message
            )

        case _:
            return UnknownApiError(f"{code}: {message}")


async def _deserialize_error_access_denied_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> AccessDeniedException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = AccessDeniedException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return AccessDeniedException(**kwargs)


async def _deserialize_error_conflict_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ConflictException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ConflictException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ConflictException(**kwargs)


async def _deserialize_error_internal_server_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> InternalServerException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = InternalServerException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return InternalServerException(**kwargs)


async def _deserialize_error_model_error_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ModelErrorException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ModelErrorException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ModelErrorException(**kwargs)


async def _deserialize_error_model_not_ready_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ModelNotReadyException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ModelNotReadyException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ModelNotReadyException(**kwargs)


async def _deserialize_error_model_stream_error_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ModelStreamErrorException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ModelStreamErrorException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ModelStreamErrorException(**kwargs)


async def _deserialize_error_model_timeout_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ModelTimeoutException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ModelTimeoutException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ModelTimeoutException(**kwargs)


async def _deserialize_error_resource_not_found_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ResourceNotFoundException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ResourceNotFoundException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ResourceNotFoundException(**kwargs)


async def _deserialize_error_service_quota_exceeded_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ServiceQuotaExceededException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ServiceQuotaExceededException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ServiceQuotaExceededException(**kwargs)


async def _deserialize_error_service_unavailable_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ServiceUnavailableException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ServiceUnavailableException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ServiceUnavailableException(**kwargs)


async def _deserialize_error_throttling_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ThrottlingException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ThrottlingException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ThrottlingException(**kwargs)


async def _deserialize_error_validation_exception(
    http_response: HTTPResponse,
    config: Config,
    parsed_body: dict[str, DocumentValue] | None,
    default_message: str,
) -> ValidationException:
    kwargs: dict[str, Any] = {"message": default_message}

    if parsed_body is None:
        body = await http_response.consume_body_async()
    else:
        body = json.dumps(parsed_body).encode("utf-8")

    if body:
        codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
        deserializer = codec.create_deserializer(body)
        body_kwargs = ValidationException.deserialize_kwargs(deserializer)
        kwargs.update(body_kwargs)

    return ValidationException(**kwargs)
