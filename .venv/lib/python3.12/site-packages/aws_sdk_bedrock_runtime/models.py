# Code generated by smithy-python-codegen DO NOT EDIT.

from dataclasses import dataclass, field
from datetime import datetime
from enum import StrEnum
import logging
from typing import Any, ClassVar, Literal, Self, Union

from smithy_core.deserializers import ShapeDeserializer
from smithy_core.documents import Document, TypeRegistry
from smithy_core.exceptions import Smithy<PERSON>xception
from smithy_core.schemas import APIOperation, Schema
from smithy_core.serializers import Shape<PERSON>erializer
from smithy_core.shapes import ShapeID

from ._private.schemas import (
    ACCESS_DENIED_EXCEPTION as _SCHEMA_ACCESS_DENIED_EXCEPTION,
    ANY_TOOL_CHOICE as _SCHEMA_ANY_TOOL_CHOICE,
    APPLY_GUARDRAIL as _SCHEMA_APPLY_GUARDRAIL,
    APPLY_GUARDRAIL_INPUT as _SCHEMA_APPLY_GUARDRAIL_INPUT,
    APPLY_GUARDRAIL_OUTPUT as _SCHEMA_APPLY_GUARDRAIL_OUTPUT,
    ASYNC_INVOKE_OUTPUT_DATA_CONFIG as _SCHEMA_ASYNC_INVOKE_OUTPUT_DATA_CONFIG,
    ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG as _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG,
    ASYNC_INVOKE_SUMMARY as _SCHEMA_ASYNC_INVOKE_SUMMARY,
    AUTO_TOOL_CHOICE as _SCHEMA_AUTO_TOOL_CHOICE,
    BIDIRECTIONAL_INPUT_PAYLOAD_PART as _SCHEMA_BIDIRECTIONAL_INPUT_PAYLOAD_PART,
    BIDIRECTIONAL_OUTPUT_PAYLOAD_PART as _SCHEMA_BIDIRECTIONAL_OUTPUT_PAYLOAD_PART,
    CACHE_POINT_BLOCK as _SCHEMA_CACHE_POINT_BLOCK,
    CONFLICT_EXCEPTION as _SCHEMA_CONFLICT_EXCEPTION,
    CONTENT_BLOCK as _SCHEMA_CONTENT_BLOCK,
    CONTENT_BLOCK_DELTA as _SCHEMA_CONTENT_BLOCK_DELTA,
    CONTENT_BLOCK_DELTA_EVENT as _SCHEMA_CONTENT_BLOCK_DELTA_EVENT,
    CONTENT_BLOCK_START as _SCHEMA_CONTENT_BLOCK_START,
    CONTENT_BLOCK_START_EVENT as _SCHEMA_CONTENT_BLOCK_START_EVENT,
    CONTENT_BLOCK_STOP_EVENT as _SCHEMA_CONTENT_BLOCK_STOP_EVENT,
    CONVERSE as _SCHEMA_CONVERSE,
    CONVERSE_INPUT as _SCHEMA_CONVERSE_INPUT,
    CONVERSE_METRICS as _SCHEMA_CONVERSE_METRICS,
    CONVERSE_OPERATION_OUTPUT as _SCHEMA_CONVERSE_OPERATION_OUTPUT,
    CONVERSE_OUTPUT as _SCHEMA_CONVERSE_OUTPUT,
    CONVERSE_STREAM as _SCHEMA_CONVERSE_STREAM,
    CONVERSE_STREAM_INPUT as _SCHEMA_CONVERSE_STREAM_INPUT,
    CONVERSE_STREAM_METADATA_EVENT as _SCHEMA_CONVERSE_STREAM_METADATA_EVENT,
    CONVERSE_STREAM_METRICS as _SCHEMA_CONVERSE_STREAM_METRICS,
    CONVERSE_STREAM_OPERATION_OUTPUT as _SCHEMA_CONVERSE_STREAM_OPERATION_OUTPUT,
    CONVERSE_STREAM_OUTPUT as _SCHEMA_CONVERSE_STREAM_OUTPUT,
    CONVERSE_STREAM_TRACE as _SCHEMA_CONVERSE_STREAM_TRACE,
    CONVERSE_TRACE as _SCHEMA_CONVERSE_TRACE,
    DOCUMENT_BLOCK as _SCHEMA_DOCUMENT_BLOCK,
    DOCUMENT_SOURCE as _SCHEMA_DOCUMENT_SOURCE,
    GET_ASYNC_INVOKE as _SCHEMA_GET_ASYNC_INVOKE,
    GET_ASYNC_INVOKE_INPUT as _SCHEMA_GET_ASYNC_INVOKE_INPUT,
    GET_ASYNC_INVOKE_OUTPUT as _SCHEMA_GET_ASYNC_INVOKE_OUTPUT,
    GUARDRAIL_ASSESSMENT as _SCHEMA_GUARDRAIL_ASSESSMENT,
    GUARDRAIL_CONFIGURATION as _SCHEMA_GUARDRAIL_CONFIGURATION,
    GUARDRAIL_CONTENT_BLOCK as _SCHEMA_GUARDRAIL_CONTENT_BLOCK,
    GUARDRAIL_CONTENT_FILTER as _SCHEMA_GUARDRAIL_CONTENT_FILTER,
    GUARDRAIL_CONTENT_POLICY_ASSESSMENT as _SCHEMA_GUARDRAIL_CONTENT_POLICY_ASSESSMENT,
    GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER as _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER,
    GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT as _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT,
    GUARDRAIL_CONVERSE_CONTENT_BLOCK as _SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK,
    GUARDRAIL_CONVERSE_IMAGE_BLOCK as _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK,
    GUARDRAIL_CONVERSE_IMAGE_SOURCE as _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_SOURCE,
    GUARDRAIL_CONVERSE_TEXT_BLOCK as _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK,
    GUARDRAIL_COVERAGE as _SCHEMA_GUARDRAIL_COVERAGE,
    GUARDRAIL_CUSTOM_WORD as _SCHEMA_GUARDRAIL_CUSTOM_WORD,
    GUARDRAIL_IMAGE_BLOCK as _SCHEMA_GUARDRAIL_IMAGE_BLOCK,
    GUARDRAIL_IMAGE_COVERAGE as _SCHEMA_GUARDRAIL_IMAGE_COVERAGE,
    GUARDRAIL_IMAGE_SOURCE as _SCHEMA_GUARDRAIL_IMAGE_SOURCE,
    GUARDRAIL_INVOCATION_METRICS as _SCHEMA_GUARDRAIL_INVOCATION_METRICS,
    GUARDRAIL_MANAGED_WORD as _SCHEMA_GUARDRAIL_MANAGED_WORD,
    GUARDRAIL_OUTPUT_CONTENT as _SCHEMA_GUARDRAIL_OUTPUT_CONTENT,
    GUARDRAIL_PII_ENTITY_FILTER as _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER,
    GUARDRAIL_REGEX_FILTER as _SCHEMA_GUARDRAIL_REGEX_FILTER,
    GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT as _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT,
    GUARDRAIL_STREAM_CONFIGURATION as _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION,
    GUARDRAIL_TEXT_BLOCK as _SCHEMA_GUARDRAIL_TEXT_BLOCK,
    GUARDRAIL_TEXT_CHARACTERS_COVERAGE as _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE,
    GUARDRAIL_TOPIC as _SCHEMA_GUARDRAIL_TOPIC,
    GUARDRAIL_TOPIC_POLICY_ASSESSMENT as _SCHEMA_GUARDRAIL_TOPIC_POLICY_ASSESSMENT,
    GUARDRAIL_TRACE_ASSESSMENT as _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT,
    GUARDRAIL_USAGE as _SCHEMA_GUARDRAIL_USAGE,
    GUARDRAIL_WORD_POLICY_ASSESSMENT as _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT,
    IMAGE_BLOCK as _SCHEMA_IMAGE_BLOCK,
    IMAGE_SOURCE as _SCHEMA_IMAGE_SOURCE,
    INFERENCE_CONFIGURATION as _SCHEMA_INFERENCE_CONFIGURATION,
    INTERNAL_SERVER_EXCEPTION as _SCHEMA_INTERNAL_SERVER_EXCEPTION,
    INVOKE_MODEL as _SCHEMA_INVOKE_MODEL,
    INVOKE_MODEL_INPUT as _SCHEMA_INVOKE_MODEL_INPUT,
    INVOKE_MODEL_OUTPUT as _SCHEMA_INVOKE_MODEL_OUTPUT,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM as _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_INPUT as _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_INPUT,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT as _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_OUTPUT as _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_OUTPUT,
    INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT as _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT,
    INVOKE_MODEL_WITH_RESPONSE_STREAM as _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM,
    INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT as _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT,
    INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT as _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT,
    LIST_ASYNC_INVOKES as _SCHEMA_LIST_ASYNC_INVOKES,
    LIST_ASYNC_INVOKES_INPUT as _SCHEMA_LIST_ASYNC_INVOKES_INPUT,
    LIST_ASYNC_INVOKES_OUTPUT as _SCHEMA_LIST_ASYNC_INVOKES_OUTPUT,
    MESSAGE as _SCHEMA_MESSAGE,
    MESSAGE_START_EVENT as _SCHEMA_MESSAGE_START_EVENT,
    MESSAGE_STOP_EVENT as _SCHEMA_MESSAGE_STOP_EVENT,
    MODEL_ERROR_EXCEPTION as _SCHEMA_MODEL_ERROR_EXCEPTION,
    MODEL_NOT_READY_EXCEPTION as _SCHEMA_MODEL_NOT_READY_EXCEPTION,
    MODEL_STREAM_ERROR_EXCEPTION as _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION,
    MODEL_TIMEOUT_EXCEPTION as _SCHEMA_MODEL_TIMEOUT_EXCEPTION,
    PAYLOAD_PART as _SCHEMA_PAYLOAD_PART,
    PERFORMANCE_CONFIGURATION as _SCHEMA_PERFORMANCE_CONFIGURATION,
    PROMPT_ROUTER_TRACE as _SCHEMA_PROMPT_ROUTER_TRACE,
    PROMPT_VARIABLE_VALUES as _SCHEMA_PROMPT_VARIABLE_VALUES,
    REASONING_CONTENT_BLOCK as _SCHEMA_REASONING_CONTENT_BLOCK,
    REASONING_CONTENT_BLOCK_DELTA as _SCHEMA_REASONING_CONTENT_BLOCK_DELTA,
    REASONING_TEXT_BLOCK as _SCHEMA_REASONING_TEXT_BLOCK,
    RESOURCE_NOT_FOUND_EXCEPTION as _SCHEMA_RESOURCE_NOT_FOUND_EXCEPTION,
    RESPONSE_STREAM as _SCHEMA_RESPONSE_STREAM,
    S3_LOCATION as _SCHEMA_S3_LOCATION,
    SERVICE_QUOTA_EXCEEDED_EXCEPTION as _SCHEMA_SERVICE_QUOTA_EXCEEDED_EXCEPTION,
    SERVICE_UNAVAILABLE_EXCEPTION as _SCHEMA_SERVICE_UNAVAILABLE_EXCEPTION,
    SPECIFIC_TOOL_CHOICE as _SCHEMA_SPECIFIC_TOOL_CHOICE,
    START_ASYNC_INVOKE as _SCHEMA_START_ASYNC_INVOKE,
    START_ASYNC_INVOKE_INPUT as _SCHEMA_START_ASYNC_INVOKE_INPUT,
    START_ASYNC_INVOKE_OUTPUT as _SCHEMA_START_ASYNC_INVOKE_OUTPUT,
    SYSTEM_CONTENT_BLOCK as _SCHEMA_SYSTEM_CONTENT_BLOCK,
    TAG as _SCHEMA_TAG,
    THROTTLING_EXCEPTION as _SCHEMA_THROTTLING_EXCEPTION,
    TOKEN_USAGE as _SCHEMA_TOKEN_USAGE,
    TOOL as _SCHEMA_TOOL,
    TOOL_CHOICE as _SCHEMA_TOOL_CHOICE,
    TOOL_CONFIGURATION as _SCHEMA_TOOL_CONFIGURATION,
    TOOL_INPUT_SCHEMA as _SCHEMA_TOOL_INPUT_SCHEMA,
    TOOL_RESULT_BLOCK as _SCHEMA_TOOL_RESULT_BLOCK,
    TOOL_RESULT_CONTENT_BLOCK as _SCHEMA_TOOL_RESULT_CONTENT_BLOCK,
    TOOL_SPECIFICATION as _SCHEMA_TOOL_SPECIFICATION,
    TOOL_USE_BLOCK as _SCHEMA_TOOL_USE_BLOCK,
    TOOL_USE_BLOCK_DELTA as _SCHEMA_TOOL_USE_BLOCK_DELTA,
    TOOL_USE_BLOCK_START as _SCHEMA_TOOL_USE_BLOCK_START,
    VALIDATION_EXCEPTION as _SCHEMA_VALIDATION_EXCEPTION,
    VIDEO_BLOCK as _SCHEMA_VIDEO_BLOCK,
    VIDEO_SOURCE as _SCHEMA_VIDEO_SOURCE,
)


logger = logging.getLogger(__name__)


class ServiceError(SmithyException):
    """Base error for all errors in the service."""

    pass


@dataclass
class ApiError(ServiceError):
    """Base error for all API errors in the service."""

    code: ClassVar[str]
    fault: ClassVar[Literal["client", "server"]]

    message: str

    def __post_init__(self) -> None:
        super().__init__(self.message)


@dataclass
class UnknownApiError(ApiError):
    """Error representing any unknown api errors."""

    code: ClassVar[str] = "Unknown"
    fault: ClassVar[Literal["client", "server"]] = "client"


@dataclass(kw_only=True)
class AccessDeniedException(ApiError):
    """
    The request is denied because you do not have sufficient permissions to perform
    the requested action. For troubleshooting this error, see `AccessDeniedException <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-access-denied>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "AccessDeniedException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_ACCESS_DENIED_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_ACCESS_DENIED_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_ACCESS_DENIED_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_ACCESS_DENIED_EXCEPTION, consumer=_consumer)
        return kwargs


def _serialize_additional_model_response_field_paths(
    serializer: ShapeSerializer, schema: Schema, value: list[str]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_string(member_schema, e)


def _deserialize_additional_model_response_field_paths(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[str]:
    result: list[str] = []
    member_schema = schema.members["member"]

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(d.read_string(member_schema))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GetAsyncInvokeInput:
    """

    :param invocation_arn:
        **[Required]** - The invocation's ARN.

    """

    invocation_arn: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GET_ASYNC_INVOKE_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["invocation_arn"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_INPUT.members["invocationArn"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GET_ASYNC_INVOKE_INPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class AsyncInvokeS3OutputDataConfig:
    """
    Asynchronous invocation output data settings.

    :param s3_uri:
        **[Required]** - An object URI starting with ``s3://``.

    :param kms_key_id:
        A KMS encryption key ID.

    :param bucket_owner:
        If the bucket belongs to another AWS account, specify that account's ID.

    """

    s3_uri: str

    kms_key_id: str | None = None
    bucket_owner: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members["s3Uri"], self.s3_uri
        )
        if self.kms_key_id is not None:
            serializer.write_string(
                _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members["kmsKeyId"],
                self.kms_key_id,
            )

        if self.bucket_owner is not None:
            serializer.write_string(
                _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members["bucketOwner"],
                self.bucket_owner,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["s3_uri"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members["s3Uri"]
                    )

                case 1:
                    kwargs["kms_key_id"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members["kmsKeyId"]
                    )

                case 2:
                    kwargs["bucket_owner"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG.members[
                            "bucketOwner"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_ASYNC_INVOKE_S3_OUTPUT_DATA_CONFIG, consumer=_consumer
        )
        return kwargs


@dataclass
class AsyncInvokeOutputDataConfigS3OutputDataConfig:
    """
    A storage location for the output data in an S3 bucket

    """

    value: AsyncInvokeS3OutputDataConfig

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_ASYNC_INVOKE_OUTPUT_DATA_CONFIG, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_ASYNC_INVOKE_OUTPUT_DATA_CONFIG.members["s3OutputDataConfig"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=AsyncInvokeS3OutputDataConfig.deserialize(deserializer))


@dataclass
class AsyncInvokeOutputDataConfigUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


AsyncInvokeOutputDataConfig = Union[
    AsyncInvokeOutputDataConfigS3OutputDataConfig | AsyncInvokeOutputDataConfigUnknown
]

"""
Asynchronous invocation output data settings.

"""


class _AsyncInvokeOutputDataConfigDeserializer:
    _result: AsyncInvokeOutputDataConfig | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> AsyncInvokeOutputDataConfig:
        self._result = None
        deserializer.read_struct(
            _SCHEMA_ASYNC_INVOKE_OUTPUT_DATA_CONFIG, self._consumer
        )

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(
                    AsyncInvokeOutputDataConfigS3OutputDataConfig.deserialize(de)
                )

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: AsyncInvokeOutputDataConfig) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


class AsyncInvokeStatus(StrEnum):
    IN_PROGRESS = "InProgress"
    COMPLETED = "Completed"
    FAILED = "Failed"


@dataclass(kw_only=True)
class GetAsyncInvokeOutput:
    """

    :param invocation_arn:
        **[Required]** - The invocation's ARN.

    :param model_arn:
        **[Required]** - The invocation's model ARN.

    :param status:
        **[Required]** - The invocation's status.

    :param submit_time:
        **[Required]** - When the invocation request was submitted.

    :param output_data_config:
        **[Required]** - Output data settings.

    :param client_request_token:
        The invocation's idempotency token.

    :param failure_message:
        An error message.

    :param last_modified_time:
        The invocation's last modified time.

    :param end_time:
        When the invocation ended.

    """

    invocation_arn: str

    model_arn: str

    status: str

    submit_time: datetime

    output_data_config: AsyncInvokeOutputDataConfig

    client_request_token: str | None = None
    failure_message: str | None = field(repr=False, default=None)
    last_modified_time: datetime | None = None
    end_time: datetime | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GET_ASYNC_INVOKE_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["invocationArn"],
            self.invocation_arn,
        )
        serializer.write_string(
            _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["modelArn"], self.model_arn
        )
        if self.client_request_token is not None:
            serializer.write_string(
                _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["clientRequestToken"],
                self.client_request_token,
            )

        serializer.write_string(
            _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["status"], self.status
        )
        if self.failure_message is not None:
            serializer.write_string(
                _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["failureMessage"],
                self.failure_message,
            )

        serializer.write_timestamp(
            _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["submitTime"], self.submit_time
        )
        if self.last_modified_time is not None:
            serializer.write_timestamp(
                _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["lastModifiedTime"],
                self.last_modified_time,
            )

        if self.end_time is not None:
            serializer.write_timestamp(
                _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["endTime"], self.end_time
            )

        serializer.write_struct(
            _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["outputDataConfig"],
            self.output_data_config,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["invocation_arn"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["invocationArn"]
                    )

                case 1:
                    kwargs["model_arn"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["modelArn"]
                    )

                case 2:
                    kwargs["client_request_token"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["clientRequestToken"]
                    )

                case 3:
                    kwargs["status"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["status"]
                    )

                case 4:
                    kwargs["failure_message"] = de.read_string(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["failureMessage"]
                    )

                case 5:
                    kwargs["submit_time"] = de.read_timestamp(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["submitTime"]
                    )

                case 6:
                    kwargs["last_modified_time"] = de.read_timestamp(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["lastModifiedTime"]
                    )

                case 7:
                    kwargs["end_time"] = de.read_timestamp(
                        _SCHEMA_GET_ASYNC_INVOKE_OUTPUT.members["endTime"]
                    )

                case 8:
                    kwargs["output_data_config"] = (
                        _AsyncInvokeOutputDataConfigDeserializer().deserialize(de)
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GET_ASYNC_INVOKE_OUTPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class InternalServerException(ApiError):
    """
    An internal server error occurred. For troubleshooting this error, see
    `InternalFailure <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-internal-failure>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "InternalServerException"
    fault: ClassVar[Literal["client", "server"]] = "server"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INTERNAL_SERVER_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_INTERNAL_SERVER_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_INTERNAL_SERVER_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_INTERNAL_SERVER_EXCEPTION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ThrottlingException(ApiError):
    """
    Your request was denied due to exceeding the account quotas for *Amazon
    Bedrock*. For troubleshooting this error, see `ThrottlingException <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-throttling-exception>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ThrottlingException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_THROTTLING_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_THROTTLING_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_THROTTLING_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_THROTTLING_EXCEPTION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ValidationException(ApiError):
    """
    The input fails to satisfy the constraints specified by *Amazon Bedrock*. For
    troubleshooting this error, see `ValidationError <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-validation-error>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ValidationException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_VALIDATION_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_VALIDATION_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_VALIDATION_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_VALIDATION_EXCEPTION, consumer=_consumer)
        return kwargs


GET_ASYNC_INVOKE = APIOperation(
    input=GetAsyncInvokeInput,
    output=GetAsyncInvokeOutput,
    schema=_SCHEMA_GET_ASYNC_INVOKE,
    input_schema=_SCHEMA_GET_ASYNC_INVOKE_INPUT,
    output_schema=_SCHEMA_GET_ASYNC_INVOKE_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


class SortAsyncInvocationBy(StrEnum):
    SUBMISSION_TIME = "SubmissionTime"


class SortOrder(StrEnum):
    ASCENDING = "Ascending"
    DESCENDING = "Descending"


@dataclass(kw_only=True)
class ListAsyncInvokesInput:
    """

    :param submit_time_after:
        Include invocations submitted after this time.

    :param submit_time_before:
        Include invocations submitted before this time.

    :param status_equals:
        Filter invocations by status.

    :param max_results:
        The maximum number of invocations to return in one page of results.

    :param next_token:
        Specify the pagination token from a previous request to retrieve the next page
        of results.

    :param sort_by:
        How to sort the response.

    :param sort_order:
        The sorting order for the response.

    """

    submit_time_after: datetime | None = None
    submit_time_before: datetime | None = None
    status_equals: str | None = None
    max_results: int | None = None
    next_token: str | None = None
    sort_by: str = "SubmissionTime"
    sort_order: str = "Descending"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_LIST_ASYNC_INVOKES_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["submit_time_after"] = de.read_timestamp(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["submitTimeAfter"]
                    )

                case 1:
                    kwargs["submit_time_before"] = de.read_timestamp(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["submitTimeBefore"]
                    )

                case 2:
                    kwargs["status_equals"] = de.read_string(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["statusEquals"]
                    )

                case 3:
                    kwargs["max_results"] = de.read_integer(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["maxResults"]
                    )

                case 4:
                    kwargs["next_token"] = de.read_string(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["nextToken"]
                    )

                case 5:
                    kwargs["sort_by"] = de.read_string(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["sortBy"]
                    )

                case 6:
                    kwargs["sort_order"] = de.read_string(
                        _SCHEMA_LIST_ASYNC_INVOKES_INPUT.members["sortOrder"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_LIST_ASYNC_INVOKES_INPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class AsyncInvokeSummary:
    """
    A summary of an asynchronous invocation.

    :param invocation_arn:
        **[Required]** - The invocation's ARN.

    :param model_arn:
        **[Required]** - The invoked model's ARN.

    :param submit_time:
        **[Required]** - When the invocation was submitted.

    :param output_data_config:
        **[Required]** - The invocation's output data settings.

    :param client_request_token:
        The invocation's idempotency token.

    :param status:
        The invocation's status.

    :param failure_message:
        An error message.

    :param last_modified_time:
        When the invocation was last modified.

    :param end_time:
        When the invocation ended.

    """

    invocation_arn: str

    model_arn: str

    submit_time: datetime

    output_data_config: AsyncInvokeOutputDataConfig

    client_request_token: str | None = None
    status: str | None = None
    failure_message: str | None = field(repr=False, default=None)
    last_modified_time: datetime | None = None
    end_time: datetime | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_ASYNC_INVOKE_SUMMARY, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_ASYNC_INVOKE_SUMMARY.members["invocationArn"], self.invocation_arn
        )
        serializer.write_string(
            _SCHEMA_ASYNC_INVOKE_SUMMARY.members["modelArn"], self.model_arn
        )
        if self.client_request_token is not None:
            serializer.write_string(
                _SCHEMA_ASYNC_INVOKE_SUMMARY.members["clientRequestToken"],
                self.client_request_token,
            )

        if self.status is not None:
            serializer.write_string(
                _SCHEMA_ASYNC_INVOKE_SUMMARY.members["status"], self.status
            )

        if self.failure_message is not None:
            serializer.write_string(
                _SCHEMA_ASYNC_INVOKE_SUMMARY.members["failureMessage"],
                self.failure_message,
            )

        serializer.write_timestamp(
            _SCHEMA_ASYNC_INVOKE_SUMMARY.members["submitTime"], self.submit_time
        )
        if self.last_modified_time is not None:
            serializer.write_timestamp(
                _SCHEMA_ASYNC_INVOKE_SUMMARY.members["lastModifiedTime"],
                self.last_modified_time,
            )

        if self.end_time is not None:
            serializer.write_timestamp(
                _SCHEMA_ASYNC_INVOKE_SUMMARY.members["endTime"], self.end_time
            )

        serializer.write_struct(
            _SCHEMA_ASYNC_INVOKE_SUMMARY.members["outputDataConfig"],
            self.output_data_config,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["invocation_arn"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["invocationArn"]
                    )

                case 1:
                    kwargs["model_arn"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["modelArn"]
                    )

                case 2:
                    kwargs["client_request_token"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["clientRequestToken"]
                    )

                case 3:
                    kwargs["status"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["status"]
                    )

                case 4:
                    kwargs["failure_message"] = de.read_string(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["failureMessage"]
                    )

                case 5:
                    kwargs["submit_time"] = de.read_timestamp(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["submitTime"]
                    )

                case 6:
                    kwargs["last_modified_time"] = de.read_timestamp(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["lastModifiedTime"]
                    )

                case 7:
                    kwargs["end_time"] = de.read_timestamp(
                        _SCHEMA_ASYNC_INVOKE_SUMMARY.members["endTime"]
                    )

                case 8:
                    kwargs["output_data_config"] = (
                        _AsyncInvokeOutputDataConfigDeserializer().deserialize(de)
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_ASYNC_INVOKE_SUMMARY, consumer=_consumer)
        return kwargs


def _serialize_async_invoke_summaries(
    serializer: ShapeSerializer, schema: Schema, value: list[AsyncInvokeSummary]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_async_invoke_summaries(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[AsyncInvokeSummary]:
    result: list[AsyncInvokeSummary] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(AsyncInvokeSummary.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class ListAsyncInvokesOutput:
    """

    :param next_token:
        Specify the pagination token from a previous request to retrieve the next page
        of results.

    :param async_invoke_summaries:
        A list of invocation summaries.

    """

    next_token: str | None = None
    async_invoke_summaries: list[AsyncInvokeSummary] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_LIST_ASYNC_INVOKES_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.next_token is not None:
            serializer.write_string(
                _SCHEMA_LIST_ASYNC_INVOKES_OUTPUT.members["nextToken"], self.next_token
            )

        if self.async_invoke_summaries is not None:
            _serialize_async_invoke_summaries(
                serializer,
                _SCHEMA_LIST_ASYNC_INVOKES_OUTPUT.members["asyncInvokeSummaries"],
                self.async_invoke_summaries,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["next_token"] = de.read_string(
                        _SCHEMA_LIST_ASYNC_INVOKES_OUTPUT.members["nextToken"]
                    )

                case 1:
                    kwargs["async_invoke_summaries"] = (
                        _deserialize_async_invoke_summaries(
                            de,
                            _SCHEMA_LIST_ASYNC_INVOKES_OUTPUT.members[
                                "asyncInvokeSummaries"
                            ],
                        )
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_LIST_ASYNC_INVOKES_OUTPUT, consumer=_consumer)
        return kwargs


LIST_ASYNC_INVOKES = APIOperation(
    input=ListAsyncInvokesInput,
    output=ListAsyncInvokesOutput,
    schema=_SCHEMA_LIST_ASYNC_INVOKES,
    input_schema=_SCHEMA_LIST_ASYNC_INVOKES_INPUT,
    output_schema=_SCHEMA_LIST_ASYNC_INVOKES_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


@dataclass(kw_only=True)
class ConflictException(ApiError):
    """
    Error occurred because of a conflict while performing an operation.

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ConflictException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONFLICT_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_CONFLICT_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_CONFLICT_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONFLICT_EXCEPTION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ResourceNotFoundException(ApiError):
    """
    The specified resource ARN was not found. For troubleshooting this error, see
    `ResourceNotFound <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-resource-not-found>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ResourceNotFoundException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESOURCE_NOT_FOUND_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_RESOURCE_NOT_FOUND_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_RESOURCE_NOT_FOUND_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_RESOURCE_NOT_FOUND_EXCEPTION, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class ServiceQuotaExceededException(ApiError):
    """
    Your request exceeds the service quota for your account. You can view your
    quotas at `Viewing service quotas <https://docs.aws.amazon.com/servicequotas/latest/userguide/gs-request-quota.html>`_.
    You can resubmit your request later.

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ServiceQuotaExceededException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SERVICE_QUOTA_EXCEEDED_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_SERVICE_QUOTA_EXCEEDED_EXCEPTION.members["message"],
                self.message,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_SERVICE_QUOTA_EXCEEDED_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_SERVICE_QUOTA_EXCEEDED_EXCEPTION, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class ServiceUnavailableException(ApiError):
    """
    The service isn't currently available. For troubleshooting this error, see
    `ServiceUnavailable <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-service-unavailable>`_
    in the Amazon Bedrock User Guide

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ServiceUnavailableException"
    fault: ClassVar[Literal["client", "server"]] = "server"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SERVICE_UNAVAILABLE_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_SERVICE_UNAVAILABLE_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_SERVICE_UNAVAILABLE_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_SERVICE_UNAVAILABLE_EXCEPTION, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class Tag:
    """
    A tag.

    :param key:
        **[Required]** - The tag's key.

    :param value:
        **[Required]** - The tag's value.

    """

    key: str

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TAG, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_TAG.members["key"], self.key)
        serializer.write_string(_SCHEMA_TAG.members["value"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["key"] = de.read_string(_SCHEMA_TAG.members["key"])

                case 1:
                    kwargs["value"] = de.read_string(_SCHEMA_TAG.members["value"])

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TAG, consumer=_consumer)
        return kwargs


def _serialize_tag_list(
    serializer: ShapeSerializer, schema: Schema, value: list[Tag]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_tag_list(deserializer: ShapeDeserializer, schema: Schema) -> list[Tag]:
    result: list[Tag] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(Tag.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class StartAsyncInvokeInput:
    """

    :param client_request_token:
        Specify idempotency token to ensure that requests are not duplicated.

    :param model_id:
        **[Required]** - The model to invoke.

    :param model_input:
        **[Required]** - Input to send to the model.

    :param output_data_config:
        **[Required]** - Where to store the output.

    :param tags:
        Tags to apply to the invocation.

    """

    client_request_token: str | None = None
    model_id: str | None = None
    model_input: Document | None = field(repr=False, default=None)
    output_data_config: AsyncInvokeOutputDataConfig | None = None
    tags: list[Tag] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_START_ASYNC_INVOKE_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.client_request_token is not None:
            serializer.write_string(
                _SCHEMA_START_ASYNC_INVOKE_INPUT.members["clientRequestToken"],
                self.client_request_token,
            )

        if self.model_id is not None:
            serializer.write_string(
                _SCHEMA_START_ASYNC_INVOKE_INPUT.members["modelId"], self.model_id
            )

        if self.model_input is not None:
            serializer.write_document(
                _SCHEMA_START_ASYNC_INVOKE_INPUT.members["modelInput"], self.model_input
            )

        if self.output_data_config is not None:
            serializer.write_struct(
                _SCHEMA_START_ASYNC_INVOKE_INPUT.members["outputDataConfig"],
                self.output_data_config,
            )

        if self.tags is not None:
            _serialize_tag_list(
                serializer, _SCHEMA_START_ASYNC_INVOKE_INPUT.members["tags"], self.tags
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["client_request_token"] = de.read_string(
                        _SCHEMA_START_ASYNC_INVOKE_INPUT.members["clientRequestToken"]
                    )

                case 1:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_START_ASYNC_INVOKE_INPUT.members["modelId"]
                    )

                case 2:
                    kwargs["model_input"] = de.read_document(
                        _SCHEMA_START_ASYNC_INVOKE_INPUT.members["modelInput"]
                    )

                case 3:
                    kwargs["output_data_config"] = (
                        _AsyncInvokeOutputDataConfigDeserializer().deserialize(de)
                    )

                case 4:
                    kwargs["tags"] = _deserialize_tag_list(
                        de, _SCHEMA_START_ASYNC_INVOKE_INPUT.members["tags"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_START_ASYNC_INVOKE_INPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class StartAsyncInvokeOutput:
    """

    :param invocation_arn:
        **[Required]** - The ARN of the invocation.

    """

    invocation_arn: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_START_ASYNC_INVOKE_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_START_ASYNC_INVOKE_OUTPUT.members["invocationArn"],
            self.invocation_arn,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["invocation_arn"] = de.read_string(
                        _SCHEMA_START_ASYNC_INVOKE_OUTPUT.members["invocationArn"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_START_ASYNC_INVOKE_OUTPUT, consumer=_consumer)
        return kwargs


START_ASYNC_INVOKE = APIOperation(
    input=StartAsyncInvokeInput,
    output=StartAsyncInvokeOutput,
    schema=_SCHEMA_START_ASYNC_INVOKE,
    input_schema=_SCHEMA_START_ASYNC_INVOKE_INPUT,
    output_schema=_SCHEMA_START_ASYNC_INVOKE_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ConflictException"
            ): ConflictException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceQuotaExceededException"
            ): ServiceQuotaExceededException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


class GuardrailImageFormat(StrEnum):
    PNG = "png"
    JPEG = "jpeg"


@dataclass
class GuardrailImageSourceBytes:
    """
    The bytes details of the guardrail image source. Object used in independent api.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_IMAGE_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(
            _SCHEMA_GUARDRAIL_IMAGE_SOURCE.members["bytes"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_blob(
                _SCHEMA_GUARDRAIL_IMAGE_SOURCE.members["bytes"]
            )
        )


@dataclass
class GuardrailImageSourceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


GuardrailImageSource = Union[GuardrailImageSourceBytes | GuardrailImageSourceUnknown]

"""
The image source (image bytes) of the guardrail image source. Object used in
independent api.

"""


class _GuardrailImageSourceDeserializer:
    _result: GuardrailImageSource | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> GuardrailImageSource:
        self._result = None
        deserializer.read_struct(_SCHEMA_GUARDRAIL_IMAGE_SOURCE, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(GuardrailImageSourceBytes.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: GuardrailImageSource) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class GuardrailImageBlock:
    """
    Contain an image which user wants guarded. This block is accepted by the
    guardrails independent API.

    :param format:
        **[Required]** - The format details for the file type of the image blocked by
        the guardrail.

    :param source:
        **[Required]** - The image source (image bytes) details of the image blocked by
        the guardrail.

    """

    format: str

    source: GuardrailImageSource = field(repr=False)

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_IMAGE_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_IMAGE_BLOCK.members["format"], self.format
        )
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_IMAGE_BLOCK.members["source"], self.source
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["format"] = de.read_string(
                        _SCHEMA_GUARDRAIL_IMAGE_BLOCK.members["format"]
                    )

                case 1:
                    kwargs["source"] = _GuardrailImageSourceDeserializer().deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_IMAGE_BLOCK, consumer=_consumer)
        return kwargs


class GuardrailContentQualifier(StrEnum):
    GROUNDING_SOURCE = "grounding_source"
    QUERY = "query"
    GUARD_CONTENT = "guard_content"


def _serialize_guardrail_content_qualifier_list(
    serializer: ShapeSerializer, schema: Schema, value: list[str]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_string(member_schema, e)


def _deserialize_guardrail_content_qualifier_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[str]:
    result: list[str] = []
    member_schema = schema.members["member"]

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(d.read_string(member_schema))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailTextBlock:
    """
    The text block to be evaluated by the guardrail.

    :param text:
        **[Required]** - The input text details to be evaluated by the guardrail.

    :param qualifiers:
        The qualifiers describing the text block.

    """

    text: str

    qualifiers: list[str] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_TEXT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_GUARDRAIL_TEXT_BLOCK.members["text"], self.text)
        if self.qualifiers is not None:
            _serialize_guardrail_content_qualifier_list(
                serializer,
                _SCHEMA_GUARDRAIL_TEXT_BLOCK.members["qualifiers"],
                self.qualifiers,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["text"] = de.read_string(
                        _SCHEMA_GUARDRAIL_TEXT_BLOCK.members["text"]
                    )

                case 1:
                    kwargs["qualifiers"] = (
                        _deserialize_guardrail_content_qualifier_list(
                            de, _SCHEMA_GUARDRAIL_TEXT_BLOCK.members["qualifiers"]
                        )
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_TEXT_BLOCK, consumer=_consumer)
        return kwargs


@dataclass
class GuardrailContentBlockText:
    """
    Text within content block to be evaluated by the guardrail.

    """

    value: GuardrailTextBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONTENT_BLOCK.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=GuardrailTextBlock.deserialize(deserializer))


@dataclass
class GuardrailContentBlockImage:
    """
    Image within guardrail content block to be evaluated by the guardrail.

    """

    value: GuardrailImageBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONTENT_BLOCK.members["image"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=GuardrailImageBlock.deserialize(deserializer))


@dataclass
class GuardrailContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


GuardrailContentBlock = Union[
    GuardrailContentBlockText
    | GuardrailContentBlockImage
    | GuardrailContentBlockUnknown
]

"""
The content block to be evaluated by the guardrail.

"""


class _GuardrailContentBlockDeserializer:
    _result: GuardrailContentBlock | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> GuardrailContentBlock:
        self._result = None
        deserializer.read_struct(_SCHEMA_GUARDRAIL_CONTENT_BLOCK, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(GuardrailContentBlockText.deserialize(de))

            case 1:
                self._set_result(GuardrailContentBlockImage.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: GuardrailContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_guardrail_content_block_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailContentBlock]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_content_block_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailContentBlock]:
    result: list[GuardrailContentBlock] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(_GuardrailContentBlockDeserializer().deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


class GuardrailOutputScope(StrEnum):
    INTERVENTIONS = "INTERVENTIONS"
    FULL = "FULL"


class GuardrailContentSource(StrEnum):
    INPUT = "INPUT"
    OUTPUT = "OUTPUT"


@dataclass(kw_only=True)
class ApplyGuardrailInput:
    """

    :param guardrail_identifier:
        **[Required]** - The guardrail identifier used in the request to apply the
        guardrail.

    :param guardrail_version:
        **[Required]** - The guardrail version used in the request to apply the
        guardrail.

    :param source:
        **[Required]** - The source of data used in the request to apply the guardrail.

    :param content:
        **[Required]** - The content details used in the request to apply the guardrail.

    :param output_scope:
        Specifies the scope of the output that you get in the response. Set to ``FULL``
        to return the entire output, including any detected and non-detected entries in
        the response for enhanced debugging.

        Note that the full output scope doesn't apply to word filters or regex in
        sensitive information filters. It does apply to all other filtering policies,
        including sensitive information with filters that can detect personally
        identifiable information (PII).

    """

    guardrail_identifier: str | None = None
    guardrail_version: str | None = None
    source: str | None = None
    content: list[GuardrailContentBlock] | None = None
    output_scope: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_APPLY_GUARDRAIL_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.source is not None:
            serializer.write_string(
                _SCHEMA_APPLY_GUARDRAIL_INPUT.members["source"], self.source
            )

        if self.content is not None:
            _serialize_guardrail_content_block_list(
                serializer,
                _SCHEMA_APPLY_GUARDRAIL_INPUT.members["content"],
                self.content,
            )

        if self.output_scope is not None:
            serializer.write_string(
                _SCHEMA_APPLY_GUARDRAIL_INPUT.members["outputScope"], self.output_scope
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail_identifier"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_INPUT.members["guardrailIdentifier"]
                    )

                case 1:
                    kwargs["guardrail_version"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_INPUT.members["guardrailVersion"]
                    )

                case 2:
                    kwargs["source"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_INPUT.members["source"]
                    )

                case 3:
                    kwargs["content"] = _deserialize_guardrail_content_block_list(
                        de, _SCHEMA_APPLY_GUARDRAIL_INPUT.members["content"]
                    )

                case 4:
                    kwargs["output_scope"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_INPUT.members["outputScope"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_APPLY_GUARDRAIL_INPUT, consumer=_consumer)
        return kwargs


class GuardrailAction(StrEnum):
    NONE = "NONE"
    GUARDRAIL_INTERVENED = "GUARDRAIL_INTERVENED"


class GuardrailContentPolicyAction(StrEnum):
    BLOCKED = "BLOCKED"
    NONE = "NONE"


class GuardrailContentFilterConfidence(StrEnum):
    NONE = "NONE"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class GuardrailContentFilterStrength(StrEnum):
    NONE = "NONE"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class GuardrailContentFilterType(StrEnum):
    INSULTS = "INSULTS"
    HATE = "HATE"
    SEXUAL = "SEXUAL"
    VIOLENCE = "VIOLENCE"
    MISCONDUCT = "MISCONDUCT"
    PROMPT_ATTACK = "PROMPT_ATTACK"


@dataclass(kw_only=True)
class GuardrailContentFilter:
    """
    The content filter for a guardrail.

    :param type:
        **[Required]** - The guardrail type.

    :param confidence:
        **[Required]** - The guardrail confidence.

    :param action:
        **[Required]** - The guardrail action.

    :param filter_strength:
        The filter strength setting for the guardrail content filter.

    :param detected:
        Indicates whether content that breaches the guardrail configuration is detected.

    """

    type: str

    confidence: str

    action: str

    filter_strength: str | None = None
    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONTENT_FILTER, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["type"], self.type
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["confidence"], self.confidence
        )
        if self.filter_strength is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["filterStrength"],
                self.filter_strength,
            )

        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["type"]
                    )

                case 1:
                    kwargs["confidence"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["confidence"]
                    )

                case 2:
                    kwargs["filter_strength"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["filterStrength"]
                    )

                case 3:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["action"]
                    )

                case 4:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_CONTENT_FILTER.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_CONTENT_FILTER, consumer=_consumer)
        return kwargs


def _serialize_guardrail_content_filter_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailContentFilter]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_content_filter_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailContentFilter]:
    result: list[GuardrailContentFilter] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailContentFilter.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailContentPolicyAssessment:
    """
    An assessment of a content policy for a guardrail.

    :param filters:
        **[Required]** - The content policy filters.

    """

    filters: list[GuardrailContentFilter]

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONTENT_POLICY_ASSESSMENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        _serialize_guardrail_content_filter_list(
            serializer,
            _SCHEMA_GUARDRAIL_CONTENT_POLICY_ASSESSMENT.members["filters"],
            self.filters,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["filters"] = _deserialize_guardrail_content_filter_list(
                        de,
                        _SCHEMA_GUARDRAIL_CONTENT_POLICY_ASSESSMENT.members["filters"],
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONTENT_POLICY_ASSESSMENT, consumer=_consumer
        )
        return kwargs


class GuardrailContextualGroundingPolicyAction(StrEnum):
    BLOCKED = "BLOCKED"
    NONE = "NONE"


class GuardrailContextualGroundingFilterType(StrEnum):
    GROUNDING = "GROUNDING"
    RELEVANCE = "RELEVANCE"


@dataclass(kw_only=True)
class GuardrailContextualGroundingFilter:
    """
    The details for the guardrails contextual grounding filter.

    :param type:
        **[Required]** - The contextual grounding filter type.

    :param threshold:
        **[Required]** - The threshold used by contextual grounding filter to determine
        whether the content is grounded or not.

    :param score:
        **[Required]** - The score generated by contextual grounding filter.

    :param action:
        **[Required]** - The action performed by the guardrails contextual grounding
        filter.

    :param detected:
        Indicates whether content that fails the contextual grounding evaluation
        (grounding or relevance score less than the corresponding threshold) was
        detected.

    """

    type: str

    threshold: float

    score: float

    action: str

    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["type"], self.type
        )
        serializer.write_double(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["threshold"],
            self.threshold,
        )
        serializer.write_double(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["score"], self.score
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["detected"],
                self.detected,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["type"]
                    )

                case 1:
                    kwargs["threshold"] = de.read_double(
                        _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members[
                            "threshold"
                        ]
                    )

                case 2:
                    kwargs["score"] = de.read_double(
                        _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["score"]
                    )

                case 3:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members["action"]
                    )

                case 4:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER.members[
                            "detected"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_FILTER, consumer=_consumer
        )
        return kwargs


def _serialize_guardrail_contextual_grounding_filters(
    serializer: ShapeSerializer,
    schema: Schema,
    value: list[GuardrailContextualGroundingFilter],
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_contextual_grounding_filters(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailContextualGroundingFilter]:
    result: list[GuardrailContextualGroundingFilter] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailContextualGroundingFilter.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailContextualGroundingPolicyAssessment:
    """
    The policy assessment details for the guardrails contextual grounding filter.

    :param filters:
        The filter details for the guardrails contextual grounding filter.

    """

    filters: list[GuardrailContextualGroundingFilter] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        if self.filters is not None:
            _serialize_guardrail_contextual_grounding_filters(
                serializer,
                _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT.members[
                    "filters"
                ],
                self.filters,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["filters"] = (
                        _deserialize_guardrail_contextual_grounding_filters(
                            de,
                            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT.members[
                                "filters"
                            ],
                        )
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONTEXTUAL_GROUNDING_POLICY_ASSESSMENT, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class GuardrailImageCoverage:
    """
    The details of the guardrail image coverage.

    :param guarded:
        The count (integer) of images guardrails guarded.

    :param total:
        Represents the total number of images (integer) that were in the request
        (guarded and unguarded).

    """

    guarded: int | None = None
    total: int | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_IMAGE_COVERAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.guarded is not None:
            serializer.write_integer(
                _SCHEMA_GUARDRAIL_IMAGE_COVERAGE.members["guarded"], self.guarded
            )

        if self.total is not None:
            serializer.write_integer(
                _SCHEMA_GUARDRAIL_IMAGE_COVERAGE.members["total"], self.total
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guarded"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_IMAGE_COVERAGE.members["guarded"]
                    )

                case 1:
                    kwargs["total"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_IMAGE_COVERAGE.members["total"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_IMAGE_COVERAGE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class GuardrailTextCharactersCoverage:
    """
    The guardrail coverage for the text characters.

    :param guarded:
        The text characters that were guarded by the guardrail coverage.

    :param total:
        The total text characters by the guardrail coverage.

    """

    guarded: int | None = None
    total: int | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.guarded is not None:
            serializer.write_integer(
                _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE.members["guarded"],
                self.guarded,
            )

        if self.total is not None:
            serializer.write_integer(
                _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE.members["total"], self.total
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guarded"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE.members["guarded"]
                    )

                case 1:
                    kwargs["total"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE.members["total"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_TEXT_CHARACTERS_COVERAGE, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class GuardrailCoverage:
    """
    The action of the guardrail coverage details.

    :param text_characters:
        The text characters of the guardrail coverage details.

    :param images:
        The guardrail coverage for images (the number of images that guardrails
        guarded).

    """

    text_characters: GuardrailTextCharactersCoverage | None = None
    images: GuardrailImageCoverage | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_COVERAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.text_characters is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_COVERAGE.members["textCharacters"],
                self.text_characters,
            )

        if self.images is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_COVERAGE.members["images"], self.images
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["text_characters"] = (
                        GuardrailTextCharactersCoverage.deserialize(de)
                    )

                case 1:
                    kwargs["images"] = GuardrailImageCoverage.deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_COVERAGE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class GuardrailUsage:
    """
    The details on the use of the guardrail.

    :param topic_policy_units:
        **[Required]** - The topic policy units processed by the guardrail.

    :param content_policy_units:
        **[Required]** - The content policy units processed by the guardrail.

    :param word_policy_units:
        **[Required]** - The word policy units processed by the guardrail.

    :param sensitive_information_policy_units:
        **[Required]** - The sensitive information policy units processed by the
        guardrail.

    :param sensitive_information_policy_free_units:
        **[Required]** - The sensitive information policy free units processed by the
        guardrail.

    :param contextual_grounding_policy_units:
        **[Required]** - The contextual grounding policy units processed by the
        guardrail.

    :param content_policy_image_units:
        The content policy image units processed by the guardrail.

    """

    topic_policy_units: int

    content_policy_units: int

    word_policy_units: int

    sensitive_information_policy_units: int

    sensitive_information_policy_free_units: int

    contextual_grounding_policy_units: int

    content_policy_image_units: int | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_USAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["topicPolicyUnits"], self.topic_policy_units
        )
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["contentPolicyUnits"],
            self.content_policy_units,
        )
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["wordPolicyUnits"], self.word_policy_units
        )
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["sensitiveInformationPolicyUnits"],
            self.sensitive_information_policy_units,
        )
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["sensitiveInformationPolicyFreeUnits"],
            self.sensitive_information_policy_free_units,
        )
        serializer.write_integer(
            _SCHEMA_GUARDRAIL_USAGE.members["contextualGroundingPolicyUnits"],
            self.contextual_grounding_policy_units,
        )
        if self.content_policy_image_units is not None:
            serializer.write_integer(
                _SCHEMA_GUARDRAIL_USAGE.members["contentPolicyImageUnits"],
                self.content_policy_image_units,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["topic_policy_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members["topicPolicyUnits"]
                    )

                case 1:
                    kwargs["content_policy_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members["contentPolicyUnits"]
                    )

                case 2:
                    kwargs["word_policy_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members["wordPolicyUnits"]
                    )

                case 3:
                    kwargs["sensitive_information_policy_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members[
                            "sensitiveInformationPolicyUnits"
                        ]
                    )

                case 4:
                    kwargs["sensitive_information_policy_free_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members[
                            "sensitiveInformationPolicyFreeUnits"
                        ]
                    )

                case 5:
                    kwargs["contextual_grounding_policy_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members[
                            "contextualGroundingPolicyUnits"
                        ]
                    )

                case 6:
                    kwargs["content_policy_image_units"] = de.read_integer(
                        _SCHEMA_GUARDRAIL_USAGE.members["contentPolicyImageUnits"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_USAGE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class GuardrailInvocationMetrics:
    """
    The invocation metrics for the guardrail.

    :param guardrail_processing_latency:
        The processing latency details for the guardrail invocation metrics.

    :param usage:
        The usage details for the guardrail invocation metrics.

    :param guardrail_coverage:
        The coverage details for the guardrail invocation metrics.

    """

    guardrail_processing_latency: int | None = None
    usage: GuardrailUsage | None = None
    guardrail_coverage: GuardrailCoverage | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_INVOCATION_METRICS, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.guardrail_processing_latency is not None:
            serializer.write_long(
                _SCHEMA_GUARDRAIL_INVOCATION_METRICS.members[
                    "guardrailProcessingLatency"
                ],
                self.guardrail_processing_latency,
            )

        if self.usage is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_INVOCATION_METRICS.members["usage"], self.usage
            )

        if self.guardrail_coverage is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_INVOCATION_METRICS.members["guardrailCoverage"],
                self.guardrail_coverage,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail_processing_latency"] = de.read_long(
                        _SCHEMA_GUARDRAIL_INVOCATION_METRICS.members[
                            "guardrailProcessingLatency"
                        ]
                    )

                case 1:
                    kwargs["usage"] = GuardrailUsage.deserialize(de)

                case 2:
                    kwargs["guardrail_coverage"] = GuardrailCoverage.deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_INVOCATION_METRICS, consumer=_consumer
        )
        return kwargs


class GuardrailSensitiveInformationPolicyAction(StrEnum):
    ANONYMIZED = "ANONYMIZED"
    BLOCKED = "BLOCKED"
    NONE = "NONE"


class GuardrailPiiEntityType(StrEnum):
    ADDRESS = "ADDRESS"
    AGE = "AGE"
    AWS_ACCESS_KEY = "AWS_ACCESS_KEY"
    AWS_SECRET_KEY = "AWS_SECRET_KEY"
    CA_HEALTH_NUMBER = "CA_HEALTH_NUMBER"
    CA_SOCIAL_INSURANCE_NUMBER = "CA_SOCIAL_INSURANCE_NUMBER"
    CREDIT_DEBIT_CARD_CVV = "CREDIT_DEBIT_CARD_CVV"
    CREDIT_DEBIT_CARD_EXPIRY = "CREDIT_DEBIT_CARD_EXPIRY"
    CREDIT_DEBIT_CARD_NUMBER = "CREDIT_DEBIT_CARD_NUMBER"
    DRIVER_ID = "DRIVER_ID"
    EMAIL = "EMAIL"
    INTERNATIONAL_BANK_ACCOUNT_NUMBER = "INTERNATIONAL_BANK_ACCOUNT_NUMBER"
    IP_ADDRESS = "IP_ADDRESS"
    LICENSE_PLATE = "LICENSE_PLATE"
    MAC_ADDRESS = "MAC_ADDRESS"
    NAME = "NAME"
    PASSWORD = "PASSWORD"
    PHONE = "PHONE"
    PIN = "PIN"
    SWIFT_CODE = "SWIFT_CODE"
    UK_NATIONAL_HEALTH_SERVICE_NUMBER = "UK_NATIONAL_HEALTH_SERVICE_NUMBER"
    UK_NATIONAL_INSURANCE_NUMBER = "UK_NATIONAL_INSURANCE_NUMBER"
    UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER = "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER"
    URL = "URL"
    USERNAME = "USERNAME"
    US_BANK_ACCOUNT_NUMBER = "US_BANK_ACCOUNT_NUMBER"
    US_BANK_ROUTING_NUMBER = "US_BANK_ROUTING_NUMBER"
    US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER = "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER"
    US_PASSPORT_NUMBER = "US_PASSPORT_NUMBER"
    US_SOCIAL_SECURITY_NUMBER = "US_SOCIAL_SECURITY_NUMBER"
    VEHICLE_IDENTIFICATION_NUMBER = "VEHICLE_IDENTIFICATION_NUMBER"


@dataclass(kw_only=True)
class GuardrailPiiEntityFilter:
    """
    A Personally Identifiable Information (PII) entity configured in a guardrail.

    :param match:
        **[Required]** - The PII entity filter match.

    :param type:
        **[Required]** - The PII entity filter type.

    :param action:
        **[Required]** - The PII entity filter action.

    :param detected:
        Indicates whether personally identifiable information (PII) that breaches the
        guardrail configuration is detected.

    """

    match: str

    type: str

    action: str

    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_PII_ENTITY_FILTER, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["match"], self.match
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["type"], self.type
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["match"] = de.read_string(
                        _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["match"]
                    )

                case 1:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["type"]
                    )

                case 2:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["action"]
                    )

                case 3:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_PII_ENTITY_FILTER, consumer=_consumer
        )
        return kwargs


def _serialize_guardrail_pii_entity_filter_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailPiiEntityFilter]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_pii_entity_filter_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailPiiEntityFilter]:
    result: list[GuardrailPiiEntityFilter] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailPiiEntityFilter.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailRegexFilter:
    """
    A Regex filter configured in a guardrail.

    :param action:
        **[Required]** - The region filter action.

    :param name:
        The regex filter name.

    :param match:
        The regesx filter match.

    :param regex:
        The regex query.

    :param detected:
        Indicates whether custom regex entities that breach the guardrail configuration
        are detected.

    """

    action: str

    name: str | None = None
    match: str | None = None
    regex: str | None = None
    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_REGEX_FILTER, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.name is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_REGEX_FILTER.members["name"], self.name
            )

        if self.match is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_REGEX_FILTER.members["match"], self.match
            )

        if self.regex is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_REGEX_FILTER.members["regex"], self.regex
            )

        serializer.write_string(
            _SCHEMA_GUARDRAIL_REGEX_FILTER.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_REGEX_FILTER.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_GUARDRAIL_REGEX_FILTER.members["name"]
                    )

                case 1:
                    kwargs["match"] = de.read_string(
                        _SCHEMA_GUARDRAIL_REGEX_FILTER.members["match"]
                    )

                case 2:
                    kwargs["regex"] = de.read_string(
                        _SCHEMA_GUARDRAIL_REGEX_FILTER.members["regex"]
                    )

                case 3:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_REGEX_FILTER.members["action"]
                    )

                case 4:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_REGEX_FILTER.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_REGEX_FILTER, consumer=_consumer)
        return kwargs


def _serialize_guardrail_regex_filter_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailRegexFilter]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_regex_filter_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailRegexFilter]:
    result: list[GuardrailRegexFilter] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailRegexFilter.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailSensitiveInformationPolicyAssessment:
    """
    The assessment for aPersonally Identifiable Information (PII) policy.

    :param pii_entities:
        **[Required]** - The PII entities in the assessment.

    :param regexes:
        **[Required]** - The regex queries in the assessment.

    """

    pii_entities: list[GuardrailPiiEntityFilter]

    regexes: list[GuardrailRegexFilter]

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        _serialize_guardrail_pii_entity_filter_list(
            serializer,
            _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT.members[
                "piiEntities"
            ],
            self.pii_entities,
        )
        _serialize_guardrail_regex_filter_list(
            serializer,
            _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT.members[
                "regexes"
            ],
            self.regexes,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["pii_entities"] = (
                        _deserialize_guardrail_pii_entity_filter_list(
                            de,
                            _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT.members[
                                "piiEntities"
                            ],
                        )
                    )

                case 1:
                    kwargs["regexes"] = _deserialize_guardrail_regex_filter_list(
                        de,
                        _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT.members[
                            "regexes"
                        ],
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_SENSITIVE_INFORMATION_POLICY_ASSESSMENT,
            consumer=_consumer,
        )
        return kwargs


class GuardrailTopicPolicyAction(StrEnum):
    BLOCKED = "BLOCKED"
    NONE = "NONE"


class GuardrailTopicType(StrEnum):
    DENY = "DENY"


@dataclass(kw_only=True)
class GuardrailTopic:
    """
    Information about a topic guardrail.

    :param name:
        **[Required]** - The name for the guardrail.

    :param type:
        **[Required]** - The type behavior that the guardrail should perform when the
        model detects the topic.

    :param action:
        **[Required]** - The action the guardrail should take when it intervenes on a
        topic.

    :param detected:
        Indicates whether topic content that breaches the guardrail configuration is
        detected.

    """

    name: str

    type: str

    action: str

    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_TOPIC, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_GUARDRAIL_TOPIC.members["name"], self.name)
        serializer.write_string(_SCHEMA_GUARDRAIL_TOPIC.members["type"], self.type)
        serializer.write_string(_SCHEMA_GUARDRAIL_TOPIC.members["action"], self.action)
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_TOPIC.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_GUARDRAIL_TOPIC.members["name"]
                    )

                case 1:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_GUARDRAIL_TOPIC.members["type"]
                    )

                case 2:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_TOPIC.members["action"]
                    )

                case 3:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_TOPIC.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_TOPIC, consumer=_consumer)
        return kwargs


def _serialize_guardrail_topic_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailTopic]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_topic_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailTopic]:
    result: list[GuardrailTopic] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailTopic.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailTopicPolicyAssessment:
    """
    A behavior assessment of a topic policy.

    :param topics:
        **[Required]** - The topics in the assessment.

    """

    topics: list[GuardrailTopic]

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_TOPIC_POLICY_ASSESSMENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        _serialize_guardrail_topic_list(
            serializer,
            _SCHEMA_GUARDRAIL_TOPIC_POLICY_ASSESSMENT.members["topics"],
            self.topics,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["topics"] = _deserialize_guardrail_topic_list(
                        de, _SCHEMA_GUARDRAIL_TOPIC_POLICY_ASSESSMENT.members["topics"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_TOPIC_POLICY_ASSESSMENT, consumer=_consumer
        )
        return kwargs


class GuardrailWordPolicyAction(StrEnum):
    BLOCKED = "BLOCKED"
    NONE = "NONE"


@dataclass(kw_only=True)
class GuardrailCustomWord:
    """
    A custom word configured in a guardrail.

    :param match:
        **[Required]** - The match for the custom word.

    :param action:
        **[Required]** - The action for the custom word.

    :param detected:
        Indicates whether custom word content that breaches the guardrail configuration
        is detected.

    """

    match: str

    action: str

    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CUSTOM_WORD, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["match"], self.match
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["match"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["match"]
                    )

                case 1:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["action"]
                    )

                case 2:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_CUSTOM_WORD.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_CUSTOM_WORD, consumer=_consumer)
        return kwargs


def _serialize_guardrail_custom_word_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailCustomWord]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_custom_word_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailCustomWord]:
    result: list[GuardrailCustomWord] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailCustomWord.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


class GuardrailManagedWordType(StrEnum):
    PROFANITY = "PROFANITY"


@dataclass(kw_only=True)
class GuardrailManagedWord:
    """
    A managed word configured in a guardrail.

    :param match:
        **[Required]** - The match for the managed word.

    :param type:
        **[Required]** - The type for the managed word.

    :param action:
        **[Required]** - The action for the managed word.

    :param detected:
        Indicates whether managed word content that breaches the guardrail configuration
        is detected.

    """

    match: str

    type: str

    action: str

    detected: bool | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_MANAGED_WORD, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_MANAGED_WORD.members["match"], self.match
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_MANAGED_WORD.members["type"], self.type
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_MANAGED_WORD.members["action"], self.action
        )
        if self.detected is not None:
            serializer.write_boolean(
                _SCHEMA_GUARDRAIL_MANAGED_WORD.members["detected"], self.detected
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["match"] = de.read_string(
                        _SCHEMA_GUARDRAIL_MANAGED_WORD.members["match"]
                    )

                case 1:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_GUARDRAIL_MANAGED_WORD.members["type"]
                    )

                case 2:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_GUARDRAIL_MANAGED_WORD.members["action"]
                    )

                case 3:
                    kwargs["detected"] = de.read_boolean(
                        _SCHEMA_GUARDRAIL_MANAGED_WORD.members["detected"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_MANAGED_WORD, consumer=_consumer)
        return kwargs


def _serialize_guardrail_managed_word_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailManagedWord]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_managed_word_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailManagedWord]:
    result: list[GuardrailManagedWord] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailManagedWord.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailWordPolicyAssessment:
    """
    The word policy assessment.

    :param custom_words:
        **[Required]** - Custom words in the assessment.

    :param managed_word_lists:
        **[Required]** - Managed word lists in the assessment.

    """

    custom_words: list[GuardrailCustomWord]

    managed_word_lists: list[GuardrailManagedWord]

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        _serialize_guardrail_custom_word_list(
            serializer,
            _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT.members["customWords"],
            self.custom_words,
        )
        _serialize_guardrail_managed_word_list(
            serializer,
            _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT.members["managedWordLists"],
            self.managed_word_lists,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["custom_words"] = _deserialize_guardrail_custom_word_list(
                        de,
                        _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT.members["customWords"],
                    )

                case 1:
                    kwargs["managed_word_lists"] = (
                        _deserialize_guardrail_managed_word_list(
                            de,
                            _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT.members[
                                "managedWordLists"
                            ],
                        )
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_WORD_POLICY_ASSESSMENT, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class GuardrailAssessment:
    """
    A behavior assessment of the guardrail policies used in a call to the Converse
    API.

    :param topic_policy:
        The topic policy.

    :param content_policy:
        The content policy.

    :param word_policy:
        The word policy.

    :param sensitive_information_policy:
        The sensitive information policy.

    :param contextual_grounding_policy:
        The contextual grounding policy used for the guardrail assessment.

    :param invocation_metrics:
        The invocation metrics for the guardrail assessment.

    """

    topic_policy: GuardrailTopicPolicyAssessment | None = None
    content_policy: GuardrailContentPolicyAssessment | None = None
    word_policy: GuardrailWordPolicyAssessment | None = None
    sensitive_information_policy: (
        GuardrailSensitiveInformationPolicyAssessment | None
    ) = None
    contextual_grounding_policy: GuardrailContextualGroundingPolicyAssessment | None = (
        None
    )
    invocation_metrics: GuardrailInvocationMetrics | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_ASSESSMENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.topic_policy is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["topicPolicy"], self.topic_policy
            )

        if self.content_policy is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["contentPolicy"],
                self.content_policy,
            )

        if self.word_policy is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["wordPolicy"], self.word_policy
            )

        if self.sensitive_information_policy is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["sensitiveInformationPolicy"],
                self.sensitive_information_policy,
            )

        if self.contextual_grounding_policy is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["contextualGroundingPolicy"],
                self.contextual_grounding_policy,
            )

        if self.invocation_metrics is not None:
            serializer.write_struct(
                _SCHEMA_GUARDRAIL_ASSESSMENT.members["invocationMetrics"],
                self.invocation_metrics,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["topic_policy"] = GuardrailTopicPolicyAssessment.deserialize(
                        de
                    )

                case 1:
                    kwargs["content_policy"] = (
                        GuardrailContentPolicyAssessment.deserialize(de)
                    )

                case 2:
                    kwargs["word_policy"] = GuardrailWordPolicyAssessment.deserialize(
                        de
                    )

                case 3:
                    kwargs["sensitive_information_policy"] = (
                        GuardrailSensitiveInformationPolicyAssessment.deserialize(de)
                    )

                case 4:
                    kwargs["contextual_grounding_policy"] = (
                        GuardrailContextualGroundingPolicyAssessment.deserialize(de)
                    )

                case 5:
                    kwargs["invocation_metrics"] = (
                        GuardrailInvocationMetrics.deserialize(de)
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_ASSESSMENT, consumer=_consumer)
        return kwargs


def _serialize_guardrail_assessment_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailAssessment]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_assessment_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailAssessment]:
    result: list[GuardrailAssessment] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailAssessment.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailOutputContent:
    """
    The output content produced by the guardrail.

    :param text:
        The specific text for the output content produced by the guardrail.

    """

    text: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_OUTPUT_CONTENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.text is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_OUTPUT_CONTENT.members["text"], self.text
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["text"] = de.read_string(
                        _SCHEMA_GUARDRAIL_OUTPUT_CONTENT.members["text"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_OUTPUT_CONTENT, consumer=_consumer)
        return kwargs


def _serialize_guardrail_output_content_list(
    serializer: ShapeSerializer, schema: Schema, value: list[GuardrailOutputContent]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_guardrail_output_content_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[GuardrailOutputContent]:
    result: list[GuardrailOutputContent] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(GuardrailOutputContent.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class ApplyGuardrailOutput:
    """

    :param usage:
        **[Required]** - The usage details in the response from the guardrail.

    :param action:
        **[Required]** - The action taken in the response from the guardrail.

    :param outputs:
        **[Required]** - The output details in the response from the guardrail.

    :param assessments:
        **[Required]** - The assessment details in the response from the guardrail.

    :param action_reason:
        The reason for the action taken when harmful content is detected.

    :param guardrail_coverage:
        The guardrail coverage details in the apply guardrail response.

    """

    usage: GuardrailUsage

    action: str

    outputs: list[GuardrailOutputContent]

    assessments: list[GuardrailAssessment]

    action_reason: str | None = None
    guardrail_coverage: GuardrailCoverage | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_APPLY_GUARDRAIL_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["usage"], self.usage
        )
        serializer.write_string(
            _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["action"], self.action
        )
        if self.action_reason is not None:
            serializer.write_string(
                _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["actionReason"],
                self.action_reason,
            )

        _serialize_guardrail_output_content_list(
            serializer, _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["outputs"], self.outputs
        )
        _serialize_guardrail_assessment_list(
            serializer,
            _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["assessments"],
            self.assessments,
        )
        if self.guardrail_coverage is not None:
            serializer.write_struct(
                _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["guardrailCoverage"],
                self.guardrail_coverage,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["usage"] = GuardrailUsage.deserialize(de)

                case 1:
                    kwargs["action"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["action"]
                    )

                case 2:
                    kwargs["action_reason"] = de.read_string(
                        _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["actionReason"]
                    )

                case 3:
                    kwargs["outputs"] = _deserialize_guardrail_output_content_list(
                        de, _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["outputs"]
                    )

                case 4:
                    kwargs["assessments"] = _deserialize_guardrail_assessment_list(
                        de, _SCHEMA_APPLY_GUARDRAIL_OUTPUT.members["assessments"]
                    )

                case 5:
                    kwargs["guardrail_coverage"] = GuardrailCoverage.deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_APPLY_GUARDRAIL_OUTPUT, consumer=_consumer)
        return kwargs


APPLY_GUARDRAIL = APIOperation(
    input=ApplyGuardrailInput,
    output=ApplyGuardrailOutput,
    schema=_SCHEMA_APPLY_GUARDRAIL,
    input_schema=_SCHEMA_APPLY_GUARDRAIL_INPUT,
    output_schema=_SCHEMA_APPLY_GUARDRAIL_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceQuotaExceededException"
            ): ServiceQuotaExceededException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


class GuardrailTrace(StrEnum):
    ENABLED = "enabled"
    DISABLED = "disabled"
    ENABLED_FULL = "enabled_full"


@dataclass(kw_only=True)
class GuardrailConfiguration:
    """
    Configuration information for a guardrail that you use with the `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
    operation.

    :param guardrail_identifier:
        **[Required]** - The identifier for the guardrail.

    :param guardrail_version:
        **[Required]** - The version of the guardrail.

    :param trace:
        The trace behavior for the guardrail.

    """

    guardrail_identifier: str

    guardrail_version: str

    trace: str = "disabled"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONFIGURATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONFIGURATION.members["guardrailIdentifier"],
            self.guardrail_identifier,
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONFIGURATION.members["guardrailVersion"],
            self.guardrail_version,
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONFIGURATION.members["trace"], self.trace
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail_identifier"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONFIGURATION.members["guardrailIdentifier"]
                    )

                case 1:
                    kwargs["guardrail_version"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONFIGURATION.members["guardrailVersion"]
                    )

                case 2:
                    kwargs["trace"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONFIGURATION.members["trace"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_CONFIGURATION, consumer=_consumer)
        return kwargs


def _serialize_non_empty_string_list(
    serializer: ShapeSerializer, schema: Schema, value: list[str]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_string(member_schema, e)


def _deserialize_non_empty_string_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[str]:
    result: list[str] = []
    member_schema = schema.members["member"]

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(d.read_string(member_schema))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class InferenceConfiguration:
    """
    Base inference parameters to pass to a model in a call to `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
    or `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_.
    For more information, see `Inference parameters for foundation models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
    .

    If you need to pass additional parameters that the model supports, use the
    ``additionalModelRequestFields`` request field in the call to ``Converse`` or ``ConverseStream``. For more information, see `Model parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
    .

    :param max_tokens:
        The maximum number of tokens to allow in the generated response. The default
        value is the maximum allowed value for the model that you are using. For more
        information, see `Inference parameters for foundation models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param temperature:
        The likelihood of the model selecting higher-probability options while
        generating a response. A lower value makes the model more likely to choose
        higher-probability options, while a higher value makes the model more likely to
        choose lower-probability options.

        The default value is the default value for the model that you are using. For
        more information, see `Inference parameters for foundation models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param top_p:
        The percentage of most-likely candidates that the model considers for the next
        token. For example, if you choose a value of 0.8 for ``topP``, the model selects
        from the top 80% of the probability distribution of tokens that could be next in
        the sequence.

        The default value is the default value for the model that you are using. For
        more information, see `Inference parameters for foundation models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param stop_sequences:
        A list of stop sequences. A stop sequence is a sequence of characters that
        causes the model to stop generating the response.

    """

    max_tokens: int | None = None
    temperature: float | None = None
    top_p: float | None = None
    stop_sequences: list[str] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INFERENCE_CONFIGURATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.max_tokens is not None:
            serializer.write_integer(
                _SCHEMA_INFERENCE_CONFIGURATION.members["maxTokens"], self.max_tokens
            )

        if self.temperature is not None:
            serializer.write_float(
                _SCHEMA_INFERENCE_CONFIGURATION.members["temperature"], self.temperature
            )

        if self.top_p is not None:
            serializer.write_float(
                _SCHEMA_INFERENCE_CONFIGURATION.members["topP"], self.top_p
            )

        if self.stop_sequences is not None:
            _serialize_non_empty_string_list(
                serializer,
                _SCHEMA_INFERENCE_CONFIGURATION.members["stopSequences"],
                self.stop_sequences,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["max_tokens"] = de.read_integer(
                        _SCHEMA_INFERENCE_CONFIGURATION.members["maxTokens"]
                    )

                case 1:
                    kwargs["temperature"] = de.read_float(
                        _SCHEMA_INFERENCE_CONFIGURATION.members["temperature"]
                    )

                case 2:
                    kwargs["top_p"] = de.read_float(
                        _SCHEMA_INFERENCE_CONFIGURATION.members["topP"]
                    )

                case 3:
                    kwargs["stop_sequences"] = _deserialize_non_empty_string_list(
                        de, _SCHEMA_INFERENCE_CONFIGURATION.members["stopSequences"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_INFERENCE_CONFIGURATION, consumer=_consumer)
        return kwargs


class CachePointType(StrEnum):
    DEFAULT = "default"


@dataclass(kw_only=True)
class CachePointBlock:
    """
    Defines a section of content to be cached for reuse in subsequent API calls.

    :param type:
        **[Required]** - Specifies the type of cache point within the CachePointBlock.

    """

    type: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CACHE_POINT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_CACHE_POINT_BLOCK.members["type"], self.type)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["type"] = de.read_string(
                        _SCHEMA_CACHE_POINT_BLOCK.members["type"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CACHE_POINT_BLOCK, consumer=_consumer)
        return kwargs


class DocumentFormat(StrEnum):
    PDF = "pdf"
    CSV = "csv"
    DOC = "doc"
    DOCX = "docx"
    XLS = "xls"
    XLSX = "xlsx"
    HTML = "html"
    TXT = "txt"
    MD = "md"


@dataclass
class DocumentSourceBytes:
    """
    The raw bytes for the document. If you use an Amazon Web Services SDK, you don't
    need to encode the bytes in base64.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_DOCUMENT_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(_SCHEMA_DOCUMENT_SOURCE.members["bytes"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_blob(_SCHEMA_DOCUMENT_SOURCE.members["bytes"])
        )


@dataclass
class DocumentSourceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


DocumentSource = Union[DocumentSourceBytes | DocumentSourceUnknown]

"""
Contains the content of a document.

"""


class _DocumentSourceDeserializer:
    _result: DocumentSource | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> DocumentSource:
        self._result = None
        deserializer.read_struct(_SCHEMA_DOCUMENT_SOURCE, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(DocumentSourceBytes.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: DocumentSource) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class DocumentBlock:
    """
    A document to include in a message.

    :param format:
        **[Required]** - The format of a document, or its extension.

    :param name:
        **[Required]** - A name for the document. The name can only contain the
        following characters:

        * Alphanumeric characters

        * Whitespace characters (no more than one in a row)

        * Hyphens

        * Parentheses

        * Square brackets

        .. note::
            This field is vulnerable to prompt injections, because the model might
            inadvertently interpret it as instructions. Therefore, we recommend that you
            specify a neutral name.

    :param source:
        **[Required]** - Contains the content of the document.

    """

    format: str

    name: str

    source: DocumentSource

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_DOCUMENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_DOCUMENT_BLOCK.members["format"], self.format)
        serializer.write_string(_SCHEMA_DOCUMENT_BLOCK.members["name"], self.name)
        serializer.write_struct(_SCHEMA_DOCUMENT_BLOCK.members["source"], self.source)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["format"] = de.read_string(
                        _SCHEMA_DOCUMENT_BLOCK.members["format"]
                    )

                case 1:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_DOCUMENT_BLOCK.members["name"]
                    )

                case 2:
                    kwargs["source"] = _DocumentSourceDeserializer().deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_DOCUMENT_BLOCK, consumer=_consumer)
        return kwargs


class GuardrailConverseImageFormat(StrEnum):
    PNG = "png"
    JPEG = "jpeg"


@dataclass
class GuardrailConverseImageSourceBytes:
    """
    The raw image bytes for the image.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONVERSE_IMAGE_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(
            _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_SOURCE.members["bytes"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_blob(
                _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_SOURCE.members["bytes"]
            )
        )


@dataclass
class GuardrailConverseImageSourceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


GuardrailConverseImageSource = Union[
    GuardrailConverseImageSourceBytes | GuardrailConverseImageSourceUnknown
]

"""
The image source (image bytes) of the guardrail converse image source.

"""


class _GuardrailConverseImageSourceDeserializer:
    _result: GuardrailConverseImageSource | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> GuardrailConverseImageSource:
        self._result = None
        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_SOURCE, self._consumer
        )

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(GuardrailConverseImageSourceBytes.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: GuardrailConverseImageSource) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class GuardrailConverseImageBlock:
    """
    An image block that contains images that you want to assess with a guardrail.

    :param format:
        **[Required]** - The format details for the image type of the guardrail converse
        image block.

    :param source:
        **[Required]** - The image source (image bytes) of the guardrail converse image
        block.

    """

    format: str

    source: GuardrailConverseImageSource = field(repr=False)

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK.members["format"], self.format
        )
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK.members["source"], self.source
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["format"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK.members["format"]
                    )

                case 1:
                    kwargs["source"] = (
                        _GuardrailConverseImageSourceDeserializer().deserialize(de)
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_IMAGE_BLOCK, consumer=_consumer
        )
        return kwargs


class GuardrailConverseContentQualifier(StrEnum):
    GROUNDING_SOURCE = "grounding_source"
    QUERY = "query"
    GUARD_CONTENT = "guard_content"


def _serialize_guardrail_converse_content_qualifier_list(
    serializer: ShapeSerializer, schema: Schema, value: list[str]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_string(member_schema, e)


def _deserialize_guardrail_converse_content_qualifier_list(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[str]:
    result: list[str] = []
    member_schema = schema.members["member"]

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(d.read_string(member_schema))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailConverseTextBlock:
    """
    A text block that contains text that you want to assess with a guardrail. For
    more information, see `GuardrailConverseContentBlock`.

    :param text:
        **[Required]** - The text that you want to guard.

    :param qualifiers:
        The qualifier details for the guardrails contextual grounding filter.

    """

    text: str

    qualifiers: list[str] | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK.members["text"], self.text
        )
        if self.qualifiers is not None:
            _serialize_guardrail_converse_content_qualifier_list(
                serializer,
                _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK.members["qualifiers"],
                self.qualifiers,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["text"] = de.read_string(
                        _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK.members["text"]
                    )

                case 1:
                    kwargs["qualifiers"] = (
                        _deserialize_guardrail_converse_content_qualifier_list(
                            de,
                            _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK.members["qualifiers"],
                        )
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_TEXT_BLOCK, consumer=_consumer
        )
        return kwargs


@dataclass
class GuardrailConverseContentBlockText:
    """
    The text to guard.

    """

    value: GuardrailConverseTextBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=GuardrailConverseTextBlock.deserialize(deserializer))


@dataclass
class GuardrailConverseContentBlockImage:
    """
    Image within converse content block to be evaluated by the guardrail.

    """

    value: GuardrailConverseImageBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK.members["image"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=GuardrailConverseImageBlock.deserialize(deserializer))


@dataclass
class GuardrailConverseContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


GuardrailConverseContentBlock = Union[
    GuardrailConverseContentBlockText
    | GuardrailConverseContentBlockImage
    | GuardrailConverseContentBlockUnknown
]

"""

A content block for selective guarding with the `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
or `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_
API operations.

"""


class _GuardrailConverseContentBlockDeserializer:
    _result: GuardrailConverseContentBlock | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> GuardrailConverseContentBlock:
        self._result = None
        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_CONVERSE_CONTENT_BLOCK, self._consumer
        )

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(GuardrailConverseContentBlockText.deserialize(de))

            case 1:
                self._set_result(GuardrailConverseContentBlockImage.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: GuardrailConverseContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


class ImageFormat(StrEnum):
    PNG = "png"
    JPEG = "jpeg"
    GIF = "gif"
    WEBP = "webp"


@dataclass
class ImageSourceBytes:
    """
    The raw image bytes for the image. If you use an AWS SDK, you don't need to
    encode the image bytes in base64.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_IMAGE_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(_SCHEMA_IMAGE_SOURCE.members["bytes"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=deserializer.read_blob(_SCHEMA_IMAGE_SOURCE.members["bytes"]))


@dataclass
class ImageSourceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ImageSource = Union[ImageSourceBytes | ImageSourceUnknown]

"""
The source for an image.

"""


class _ImageSourceDeserializer:
    _result: ImageSource | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ImageSource:
        self._result = None
        deserializer.read_struct(_SCHEMA_IMAGE_SOURCE, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ImageSourceBytes.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ImageSource) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ImageBlock:
    """
    Image content for a message.

    :param format:
        **[Required]** - The format of the image.

    :param source:
        **[Required]** - The source for the image.

    """

    format: str

    source: ImageSource

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_IMAGE_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_IMAGE_BLOCK.members["format"], self.format)
        serializer.write_struct(_SCHEMA_IMAGE_BLOCK.members["source"], self.source)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["format"] = de.read_string(
                        _SCHEMA_IMAGE_BLOCK.members["format"]
                    )

                case 1:
                    kwargs["source"] = _ImageSourceDeserializer().deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_IMAGE_BLOCK, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ReasoningTextBlock:
    """
    Contains the reasoning that the model used to return the output.

    :param text:
        **[Required]** - The reasoning that the model used to return the output.

    :param signature:
        A token that verifies that the reasoning text was generated by the model. If you
        pass a reasoning block back to the API in a multi-turn conversation, include the
        text and its signature unmodified.

    """

    text: str

    signature: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_TEXT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_REASONING_TEXT_BLOCK.members["text"], self.text)
        if self.signature is not None:
            serializer.write_string(
                _SCHEMA_REASONING_TEXT_BLOCK.members["signature"], self.signature
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["text"] = de.read_string(
                        _SCHEMA_REASONING_TEXT_BLOCK.members["text"]
                    )

                case 1:
                    kwargs["signature"] = de.read_string(
                        _SCHEMA_REASONING_TEXT_BLOCK.members["signature"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_REASONING_TEXT_BLOCK, consumer=_consumer)
        return kwargs


@dataclass
class ReasoningContentBlockReasoningText:
    """
    The reasoning that the model used to return the output.

    """

    value: ReasoningTextBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_REASONING_CONTENT_BLOCK.members["reasoningText"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ReasoningTextBlock.deserialize(deserializer))


@dataclass
class ReasoningContentBlockRedactedContent:
    """
    The content in the reasoning that was encrypted by the model provider for safety
    reasons. The encryption doesn't affect the quality of responses.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(
            _SCHEMA_REASONING_CONTENT_BLOCK.members["redactedContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_blob(
                _SCHEMA_REASONING_CONTENT_BLOCK.members["redactedContent"]
            )
        )


@dataclass
class ReasoningContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ReasoningContentBlock = Union[
    ReasoningContentBlockReasoningText
    | ReasoningContentBlockRedactedContent
    | ReasoningContentBlockUnknown
]

"""
Contains content regarding the reasoning that is carried out by the model with
respect to the content in the content block. Reasoning refers to a Chain of
Thought (CoT) that the model generates to enhance the accuracy of its final
response.

"""


class _ReasoningContentBlockDeserializer:
    _result: ReasoningContentBlock | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ReasoningContentBlock:
        self._result = None
        deserializer.read_struct(_SCHEMA_REASONING_CONTENT_BLOCK, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ReasoningContentBlockReasoningText.deserialize(de))

            case 1:
                self._set_result(ReasoningContentBlockRedactedContent.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ReasoningContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


class VideoFormat(StrEnum):
    MKV = "mkv"
    MOV = "mov"
    MP4 = "mp4"
    WEBM = "webm"
    FLV = "flv"
    MPEG = "mpeg"
    MPG = "mpg"
    WMV = "wmv"
    THREE_GP = "three_gp"


@dataclass(kw_only=True)
class S3Location:
    """
    A storage location in an S3 bucket.

    :param uri:
        **[Required]** - An object URI starting with ``s3://``.

    :param bucket_owner:
        If the bucket belongs to another AWS account, specify that account's ID.

    """

    uri: str

    bucket_owner: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_S3_LOCATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_S3_LOCATION.members["uri"], self.uri)
        if self.bucket_owner is not None:
            serializer.write_string(
                _SCHEMA_S3_LOCATION.members["bucketOwner"], self.bucket_owner
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["uri"] = de.read_string(_SCHEMA_S3_LOCATION.members["uri"])

                case 1:
                    kwargs["bucket_owner"] = de.read_string(
                        _SCHEMA_S3_LOCATION.members["bucketOwner"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_S3_LOCATION, consumer=_consumer)
        return kwargs


@dataclass
class VideoSourceBytes:
    """
    Video content encoded in base64.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_VIDEO_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(_SCHEMA_VIDEO_SOURCE.members["bytes"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=deserializer.read_blob(_SCHEMA_VIDEO_SOURCE.members["bytes"]))


@dataclass
class VideoSourceS3Location:
    """
    The location of a video object in an S3 bucket.

    """

    value: S3Location

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_VIDEO_SOURCE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_VIDEO_SOURCE.members["s3Location"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=S3Location.deserialize(deserializer))


@dataclass
class VideoSourceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


VideoSource = Union[VideoSourceBytes | VideoSourceS3Location | VideoSourceUnknown]

"""
A video source. You can upload a smaller video as a base64-encoded string as
long as the encoded file is less than 25MB. You can also transfer videos up to
1GB in size from an S3 bucket.

"""


class _VideoSourceDeserializer:
    _result: VideoSource | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> VideoSource:
        self._result = None
        deserializer.read_struct(_SCHEMA_VIDEO_SOURCE, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(VideoSourceBytes.deserialize(de))

            case 1:
                self._set_result(VideoSourceS3Location.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: VideoSource) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class VideoBlock:
    """
    A video block.

    :param format:
        **[Required]** - The block's format.

    :param source:
        **[Required]** - The block's source.

    """

    format: str

    source: VideoSource

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_VIDEO_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_VIDEO_BLOCK.members["format"], self.format)
        serializer.write_struct(_SCHEMA_VIDEO_BLOCK.members["source"], self.source)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["format"] = de.read_string(
                        _SCHEMA_VIDEO_BLOCK.members["format"]
                    )

                case 1:
                    kwargs["source"] = _VideoSourceDeserializer().deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_VIDEO_BLOCK, consumer=_consumer)
        return kwargs


@dataclass
class ToolResultContentBlockJson:
    """
    A tool result that is JSON format data.

    """

    value: Document

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_document(
            _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["json"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_document(
                _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["json"]
            )
        )


@dataclass
class ToolResultContentBlockText:
    """
    A tool result that is text.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(
                _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["text"]
            )
        )


@dataclass
class ToolResultContentBlockImage:
    """
    A tool result that is an image.

    .. note::
        This field is only supported by Anthropic Claude 3 models.

    """

    value: ImageBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["image"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ImageBlock.deserialize(deserializer))


@dataclass
class ToolResultContentBlockDocument:
    """
    A tool result that is a document.

    """

    value: DocumentBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["document"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=DocumentBlock.deserialize(deserializer))


@dataclass
class ToolResultContentBlockVideo:
    """
    A tool result that is video.

    """

    value: VideoBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_TOOL_RESULT_CONTENT_BLOCK.members["video"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=VideoBlock.deserialize(deserializer))


@dataclass
class ToolResultContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ToolResultContentBlock = Union[
    ToolResultContentBlockJson
    | ToolResultContentBlockText
    | ToolResultContentBlockImage
    | ToolResultContentBlockDocument
    | ToolResultContentBlockVideo
    | ToolResultContentBlockUnknown
]

"""
The tool result content block.

"""


class _ToolResultContentBlockDeserializer:
    _result: ToolResultContentBlock | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ToolResultContentBlock:
        self._result = None
        deserializer.read_struct(_SCHEMA_TOOL_RESULT_CONTENT_BLOCK, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ToolResultContentBlockJson.deserialize(de))

            case 1:
                self._set_result(ToolResultContentBlockText.deserialize(de))

            case 2:
                self._set_result(ToolResultContentBlockImage.deserialize(de))

            case 3:
                self._set_result(ToolResultContentBlockDocument.deserialize(de))

            case 4:
                self._set_result(ToolResultContentBlockVideo.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ToolResultContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_tool_result_content_blocks(
    serializer: ShapeSerializer, schema: Schema, value: list[ToolResultContentBlock]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_tool_result_content_blocks(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[ToolResultContentBlock]:
    result: list[ToolResultContentBlock] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(_ToolResultContentBlockDeserializer().deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


class ToolResultStatus(StrEnum):
    SUCCESS = "success"
    ERROR = "error"


@dataclass(kw_only=True)
class ToolResultBlock:
    """
    A tool result block that contains the results for a tool request that the model
    previously made.

    :param tool_use_id:
        **[Required]** - The ID of the tool request that this is the result for.

    :param content:
        **[Required]** - The content for tool result content block.

    :param status:
        The status for the tool result content block.

        .. note::
            This field is only supported Anthropic Claude 3 models.

    """

    tool_use_id: str

    content: list[ToolResultContentBlock]

    status: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_RESULT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_TOOL_RESULT_BLOCK.members["toolUseId"], self.tool_use_id
        )
        _serialize_tool_result_content_blocks(
            serializer, _SCHEMA_TOOL_RESULT_BLOCK.members["content"], self.content
        )
        if self.status is not None:
            serializer.write_string(
                _SCHEMA_TOOL_RESULT_BLOCK.members["status"], self.status
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["tool_use_id"] = de.read_string(
                        _SCHEMA_TOOL_RESULT_BLOCK.members["toolUseId"]
                    )

                case 1:
                    kwargs["content"] = _deserialize_tool_result_content_blocks(
                        de, _SCHEMA_TOOL_RESULT_BLOCK.members["content"]
                    )

                case 2:
                    kwargs["status"] = de.read_string(
                        _SCHEMA_TOOL_RESULT_BLOCK.members["status"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_RESULT_BLOCK, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ToolUseBlock:
    """
    A tool use content block. Contains information about a tool that the model is
    requesting be run., The model uses the result from the tool to generate a
    response.

    :param tool_use_id:
        **[Required]** - The ID for the tool request.

    :param name:
        **[Required]** - The name of the tool that the model wants to use.

    :param input:
        **[Required]** - The input to pass to the tool.

    """

    tool_use_id: str

    name: str

    input: Document

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_USE_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_TOOL_USE_BLOCK.members["toolUseId"], self.tool_use_id
        )
        serializer.write_string(_SCHEMA_TOOL_USE_BLOCK.members["name"], self.name)
        serializer.write_document(_SCHEMA_TOOL_USE_BLOCK.members["input"], self.input)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["tool_use_id"] = de.read_string(
                        _SCHEMA_TOOL_USE_BLOCK.members["toolUseId"]
                    )

                case 1:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_TOOL_USE_BLOCK.members["name"]
                    )

                case 2:
                    kwargs["input"] = de.read_document(
                        _SCHEMA_TOOL_USE_BLOCK.members["input"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_USE_BLOCK, consumer=_consumer)
        return kwargs


@dataclass
class ContentBlockText:
    """
    Text to include in the message.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_CONTENT_BLOCK.members["text"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(_SCHEMA_CONTENT_BLOCK.members["text"])
        )


@dataclass
class ContentBlockImage:
    """
    Image to include in the message.

    .. note::
        This field is only supported by Anthropic Claude 3 models.

    """

    value: ImageBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["image"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ImageBlock.deserialize(deserializer))


@dataclass
class ContentBlockDocument:
    """
    A document to include in the message.

    """

    value: DocumentBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["document"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=DocumentBlock.deserialize(deserializer))


@dataclass
class ContentBlockVideo:
    """
    Video to include in the message.

    """

    value: VideoBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["video"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=VideoBlock.deserialize(deserializer))


@dataclass
class ContentBlockToolUse:
    """
    Information about a tool use request from a model.

    """

    value: ToolUseBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["toolUse"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ToolUseBlock.deserialize(deserializer))


@dataclass
class ContentBlockToolResult:
    """
    The result for a tool request that a model makes.

    """

    value: ToolResultBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["toolResult"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ToolResultBlock.deserialize(deserializer))


@dataclass
class ContentBlockGuardContent:
    """
    Contains the content to assess with the guardrail. If you don't specify
    ``guardContent`` in a call to the Converse API, the guardrail (if passed in the
    Converse API) assesses the entire message.

    For more information, see *Use a guardrail with the Converse API* in the *Amazon
    Bedrock User Guide*.

    """

    value: GuardrailConverseContentBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK.members["guardContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=_GuardrailConverseContentBlockDeserializer().deserialize(deserializer)
        )


@dataclass
class ContentBlockCachePoint:
    """
    CachePoint to include in the message.

    """

    value: CachePointBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK.members["cachePoint"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=CachePointBlock.deserialize(deserializer))


@dataclass
class ContentBlockReasoningContent:
    """
    Contains content regarding the reasoning that is carried out by the model.
    Reasoning refers to a Chain of Thought (CoT) that the model generates to enhance
    the accuracy of its final response.

    """

    value: ReasoningContentBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK.members["reasoningContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=_ReasoningContentBlockDeserializer().deserialize(deserializer))


@dataclass
class ContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ContentBlock = Union[
    ContentBlockText
    | ContentBlockImage
    | ContentBlockDocument
    | ContentBlockVideo
    | ContentBlockToolUse
    | ContentBlockToolResult
    | ContentBlockGuardContent
    | ContentBlockCachePoint
    | ContentBlockReasoningContent
    | ContentBlockUnknown
]

"""
A block of content for a message that you pass to, or receive from, a model with
the `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
or `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_
API operations.

"""


class _ContentBlockDeserializer:
    _result: ContentBlock | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ContentBlock:
        self._result = None
        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ContentBlockText.deserialize(de))

            case 1:
                self._set_result(ContentBlockImage.deserialize(de))

            case 2:
                self._set_result(ContentBlockDocument.deserialize(de))

            case 3:
                self._set_result(ContentBlockVideo.deserialize(de))

            case 4:
                self._set_result(ContentBlockToolUse.deserialize(de))

            case 5:
                self._set_result(ContentBlockToolResult.deserialize(de))

            case 6:
                self._set_result(ContentBlockGuardContent.deserialize(de))

            case 7:
                self._set_result(ContentBlockCachePoint.deserialize(de))

            case 8:
                self._set_result(ContentBlockReasoningContent.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_content_blocks(
    serializer: ShapeSerializer, schema: Schema, value: list[ContentBlock]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_content_blocks(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[ContentBlock]:
    result: list[ContentBlock] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(_ContentBlockDeserializer().deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


class ConversationRole(StrEnum):
    USER = "user"
    ASSISTANT = "assistant"


@dataclass(kw_only=True)
class Message:
    """
    A message input, or returned from, a call to `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
    or `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_
    .

    :param role:
        **[Required]** - The role that the message plays in the message.

    :param content:
        **[Required]** - The message content. Note the following restrictions:

        * You can include up to 20 images. Each image's size, height, and width must be
          no more than 3.75 MB, 8000 px, and 8000 px, respectively.

        * You can include up to five documents. Each document's size must be no more
          than 4.5 MB.

        * If you include a ``ContentBlock`` with a ``document`` field in the array, you
          must also include a ``ContentBlock`` with a ``text`` field.

        * You can only include images and documents if the ``role`` is ``user``.

    """

    role: str

    content: list[ContentBlock]

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MESSAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_MESSAGE.members["role"], self.role)
        _serialize_content_blocks(
            serializer, _SCHEMA_MESSAGE.members["content"], self.content
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["role"] = de.read_string(_SCHEMA_MESSAGE.members["role"])

                case 1:
                    kwargs["content"] = _deserialize_content_blocks(
                        de, _SCHEMA_MESSAGE.members["content"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MESSAGE, consumer=_consumer)
        return kwargs


def _serialize_messages(
    serializer: ShapeSerializer, schema: Schema, value: list[Message]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_messages(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[Message]:
    result: list[Message] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(Message.deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


class PerformanceConfigLatency(StrEnum):
    STANDARD = "standard"
    OPTIMIZED = "optimized"


@dataclass(kw_only=True)
class PerformanceConfiguration:
    """
    Performance settings for a model.

    :param latency:
        To use a latency-optimized version of the model, set to ``optimized``.

    """

    latency: str = "standard"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_PERFORMANCE_CONFIGURATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_PERFORMANCE_CONFIGURATION.members["latency"], self.latency
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["latency"] = de.read_string(
                        _SCHEMA_PERFORMANCE_CONFIGURATION.members["latency"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_PERFORMANCE_CONFIGURATION, consumer=_consumer)
        return kwargs


@dataclass
class PromptVariableValuesText:
    """
    The text value that the variable maps to.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_PROMPT_VARIABLE_VALUES, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_PROMPT_VARIABLE_VALUES.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(
                _SCHEMA_PROMPT_VARIABLE_VALUES.members["text"]
            )
        )


@dataclass
class PromptVariableValuesUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


PromptVariableValues = Union[PromptVariableValuesText | PromptVariableValuesUnknown]

"""
Contains a map of variables in a prompt from Prompt management to an object
containing the values to fill in for them when running model invocation. For
more information, see `How Prompt management works <https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-how.html>`_
.

"""


class _PromptVariableValuesDeserializer:
    _result: PromptVariableValues | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> PromptVariableValues:
        self._result = None
        deserializer.read_struct(_SCHEMA_PROMPT_VARIABLE_VALUES, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(PromptVariableValuesText.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: PromptVariableValues) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_prompt_variable_map(
    serializer: ShapeSerializer, schema: Schema, value: dict[str, PromptVariableValues]
) -> None:
    with serializer.begin_map(schema, len(value)) as m:
        value_schema = schema.members["value"]
        for k, v in value.items():
            m.entry(k, lambda vs: vs.write_struct(value_schema, v))


def _deserialize_prompt_variable_map(
    deserializer: ShapeDeserializer, schema: Schema
) -> dict[str, PromptVariableValues]:
    result: dict[str, PromptVariableValues] = {}
    value_schema = schema.members["value"]

    def _read_value(k: str, d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result[k] = _PromptVariableValuesDeserializer().deserialize(d)

    deserializer.read_map(schema, _read_value)
    return result


def _serialize_request_metadata(
    serializer: ShapeSerializer, schema: Schema, value: dict[str, str]
) -> None:
    with serializer.begin_map(schema, len(value)) as m:
        value_schema = schema.members["value"]
        for k, v in value.items():
            m.entry(k, lambda vs: vs.write_string(value_schema, v))


def _deserialize_request_metadata(
    deserializer: ShapeDeserializer, schema: Schema
) -> dict[str, str]:
    result: dict[str, str] = {}
    value_schema = schema.members["value"]

    def _read_value(k: str, d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result[k] = d.read_string(value_schema)

    deserializer.read_map(schema, _read_value)
    return result


@dataclass
class SystemContentBlockText:
    """
    A system prompt for the model.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SYSTEM_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_SYSTEM_CONTENT_BLOCK.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(_SCHEMA_SYSTEM_CONTENT_BLOCK.members["text"])
        )


@dataclass
class SystemContentBlockGuardContent:
    """
    A content block to assess with the guardrail. Use with the `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
    or `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_
    API operations.

    For more information, see *Use a guardrail with the Converse API* in the *Amazon
    Bedrock User Guide*.

    """

    value: GuardrailConverseContentBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SYSTEM_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_SYSTEM_CONTENT_BLOCK.members["guardContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=_GuardrailConverseContentBlockDeserializer().deserialize(deserializer)
        )


@dataclass
class SystemContentBlockCachePoint:
    """
    CachePoint to include in the system prompt.

    """

    value: CachePointBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SYSTEM_CONTENT_BLOCK, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_SYSTEM_CONTENT_BLOCK.members["cachePoint"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=CachePointBlock.deserialize(deserializer))


@dataclass
class SystemContentBlockUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


SystemContentBlock = Union[
    SystemContentBlockText
    | SystemContentBlockGuardContent
    | SystemContentBlockCachePoint
    | SystemContentBlockUnknown
]

"""
A system content block.

"""


class _SystemContentBlockDeserializer:
    _result: SystemContentBlock | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> SystemContentBlock:
        self._result = None
        deserializer.read_struct(_SCHEMA_SYSTEM_CONTENT_BLOCK, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(SystemContentBlockText.deserialize(de))

            case 1:
                self._set_result(SystemContentBlockGuardContent.deserialize(de))

            case 2:
                self._set_result(SystemContentBlockCachePoint.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: SystemContentBlock) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_system_content_blocks(
    serializer: ShapeSerializer, schema: Schema, value: list[SystemContentBlock]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_system_content_blocks(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[SystemContentBlock]:
    result: list[SystemContentBlock] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(_SystemContentBlockDeserializer().deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class AnyToolChoice:
    """
    The model must request at least one tool (no text is generated). For example,
    ``{"any" : {}}``.

    """

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_ANY_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_ANY_TOOL_CHOICE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class AutoToolChoice:
    """
    The Model automatically decides if a tool should be called or whether to
    generate text instead. For example, ``{"auto" : {}}``.

    """

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_AUTO_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_AUTO_TOOL_CHOICE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class SpecificToolChoice:
    """
    The model must request a specific tool. For example, ``{"tool" : {"name" : "Your
    tool name"}}``.

    .. note::
        This field is only supported by Anthropic Claude 3 models.

    :param name:
        **[Required]** - The name of the tool that the model must request.

    """

    name: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_SPECIFIC_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_SPECIFIC_TOOL_CHOICE.members["name"], self.name)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_SPECIFIC_TOOL_CHOICE.members["name"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_SPECIFIC_TOOL_CHOICE, consumer=_consumer)
        return kwargs


@dataclass
class ToolChoiceAuto:
    """
    (Default). The Model automatically decides if a tool should be called or whether
    to generate text instead.

    """

    value: AutoToolChoice

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE.members["auto"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=AutoToolChoice.deserialize(deserializer))


@dataclass
class ToolChoiceAny:
    """
    The model must request at least one tool (no text is generated).

    """

    value: AnyToolChoice

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE.members["any"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=AnyToolChoice.deserialize(deserializer))


@dataclass
class ToolChoiceTool:
    """
    The Model must request the specified tool. Only supported by Anthropic Claude 3
    models.

    """

    value: SpecificToolChoice

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CHOICE.members["tool"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=SpecificToolChoice.deserialize(deserializer))


@dataclass
class ToolChoiceUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ToolChoice = Union[ToolChoiceAuto | ToolChoiceAny | ToolChoiceTool | ToolChoiceUnknown]

"""
Determines which tools the model should request in a call to ``Converse`` or
``ConverseStream``. ``ToolChoice`` is only supported by Anthropic Claude 3
models and by Mistral AI Mistral Large.

"""


class _ToolChoiceDeserializer:
    _result: ToolChoice | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ToolChoice:
        self._result = None
        deserializer.read_struct(_SCHEMA_TOOL_CHOICE, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ToolChoiceAuto.deserialize(de))

            case 1:
                self._set_result(ToolChoiceAny.deserialize(de))

            case 2:
                self._set_result(ToolChoiceTool.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ToolChoice) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass
class ToolInputSchemaJson:
    """
    The JSON schema for the tool. For more information, see `JSON Schema Reference <https://json-schema.org/understanding-json-schema/reference>`_
    .

    """

    value: Document

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_INPUT_SCHEMA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_document(_SCHEMA_TOOL_INPUT_SCHEMA.members["json"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_document(_SCHEMA_TOOL_INPUT_SCHEMA.members["json"])
        )


@dataclass
class ToolInputSchemaUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ToolInputSchema = Union[ToolInputSchemaJson | ToolInputSchemaUnknown]

"""
The schema for the tool. The top level schema type must be ``object``.

"""


class _ToolInputSchemaDeserializer:
    _result: ToolInputSchema | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ToolInputSchema:
        self._result = None
        deserializer.read_struct(_SCHEMA_TOOL_INPUT_SCHEMA, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ToolInputSchemaJson.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ToolInputSchema) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ToolSpecification:
    """
    The specification for the tool.

    :param name:
        **[Required]** - The name for the tool.

    :param input_schema:
        **[Required]** - The input schema for the tool in JSON format.

    :param description:
        The description for the tool.

    """

    name: str

    input_schema: ToolInputSchema

    description: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_SPECIFICATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_TOOL_SPECIFICATION.members["name"], self.name)
        if self.description is not None:
            serializer.write_string(
                _SCHEMA_TOOL_SPECIFICATION.members["description"], self.description
            )

        serializer.write_struct(
            _SCHEMA_TOOL_SPECIFICATION.members["inputSchema"], self.input_schema
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_TOOL_SPECIFICATION.members["name"]
                    )

                case 1:
                    kwargs["description"] = de.read_string(
                        _SCHEMA_TOOL_SPECIFICATION.members["description"]
                    )

                case 2:
                    kwargs["input_schema"] = _ToolInputSchemaDeserializer().deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_SPECIFICATION, consumer=_consumer)
        return kwargs


@dataclass
class ToolToolSpec:
    """
    The specfication for the tool.

    """

    value: ToolSpecification

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL.members["toolSpec"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ToolSpecification.deserialize(deserializer))


@dataclass
class ToolCachePoint:
    """
    CachePoint to include in the tool configuration.

    """

    value: CachePointBlock

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL.members["cachePoint"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=CachePointBlock.deserialize(deserializer))


@dataclass
class ToolUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


Tool = Union[ToolToolSpec | ToolCachePoint | ToolUnknown]

"""
Information about a tool that you can use with the Converse API. For more
information, see `Tool use (function calling) <https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html>`_
in the Amazon Bedrock User Guide.

"""


class _ToolDeserializer:
    _result: Tool | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> Tool:
        self._result = None
        deserializer.read_struct(_SCHEMA_TOOL, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ToolToolSpec.deserialize(de))

            case 1:
                self._set_result(ToolCachePoint.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: Tool) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


def _serialize_tools(
    serializer: ShapeSerializer, schema: Schema, value: list[Tool]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_struct(member_schema, e)


def _deserialize_tools(deserializer: ShapeDeserializer, schema: Schema) -> list[Tool]:
    result: list[Tool] = []

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(_ToolDeserializer().deserialize(d))

    deserializer.read_list(schema, _read_value)
    return result


@dataclass(kw_only=True)
class ToolConfiguration:
    """
    Configuration information for the tools that you pass to a model. For more
    information, see `Tool use (function calling) <https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html>`_
    in the Amazon Bedrock User Guide.

    :param tools:
        **[Required]** - An array of tools that you want to pass to a model.

    :param tool_choice:
        If supported by model, forces the model to request a tool.

    """

    tools: list[Tool]

    tool_choice: ToolChoice | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_CONFIGURATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        _serialize_tools(
            serializer, _SCHEMA_TOOL_CONFIGURATION.members["tools"], self.tools
        )
        if self.tool_choice is not None:
            serializer.write_struct(
                _SCHEMA_TOOL_CONFIGURATION.members["toolChoice"], self.tool_choice
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["tools"] = _deserialize_tools(
                        de, _SCHEMA_TOOL_CONFIGURATION.members["tools"]
                    )

                case 1:
                    kwargs["tool_choice"] = _ToolChoiceDeserializer().deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_CONFIGURATION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseInput:
    """

    :param model_id:
        **[Required]** - Specifies the model or throughput with which to run inference,
        or the prompt resource to use in inference. The value depends on the resource
        that you use:

        * If you use a base model, specify the model ID or its ARN. For a list of model
          IDs for base models, see `Amazon Bedrock base model IDs (on-demand throughput) <https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns>`_
          in the Amazon Bedrock User Guide.

        * If you use an inference profile, specify the inference profile ID or its ARN.
          For a list of inference profile IDs, see `Supported Regions and models for cross-region inference <https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a provisioned model, specify the ARN of the Provisioned Throughput.
          For more information, see `Run inference using a Provisioned Throughput <https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a custom model, first purchase Provisioned Throughput for it. Then
          specify the ARN of the resulting provisioned model. For more information, see
          `Use a custom model in Amazon Bedrock <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html>`_
          in the Amazon Bedrock User Guide.

        * To include a prompt that was defined in `Prompt management <https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management.html>`_,
          specify the ARN of the prompt version to use.

        The Converse API doesn't support `imported models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html>`_
        .

    :param messages:
        The messages that you want to send to the model.

    :param system:
        A prompt that provides instructions or context to the model about the task it
        should perform, or the persona it should adopt during the conversation.

    :param inference_config:
        Inference parameters to pass to the model. ``Converse`` and ``ConverseStream``
        support a base set of inference parameters. If you need to pass additional
        parameters that the model supports, use the ``additionalModelRequestFields``
        request field.

    :param tool_config:
        Configuration information for the tools that the model can use when generating a
        response.

        For information about models that support tool use, see `Supported models and model features <https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html#conversation-inference-supported-models-features>`_
        .

    :param guardrail_config:
        Configuration information for a guardrail that you want to use in the request.
        If you include ``guardContent`` blocks in the ``content`` field in the
        ``messages`` field, the guardrail operates only on those messages. If you
        include no ``guardContent`` blocks, the guardrail operates on all messages in
        the request body and in any included prompt resource.

    :param additional_model_request_fields:
        Additional inference parameters that the model supports, beyond the base set of
        inference parameters that ``Converse`` and ``ConverseStream`` support in the ``inferenceConfig`` field. For more information, see `Model parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param prompt_variables:
        Contains a map of variables in a prompt from Prompt management to objects
        containing the values to fill in for them when running model invocation. This
        field is ignored if you don't specify a prompt resource in the ``modelId``
        field.

    :param additional_model_response_field_paths:
        Additional model parameters field paths to return in the response. ``Converse``
        and ``ConverseStream`` return the requested fields as a JSON Pointer object in
        the ``additionalModelResponseFields`` field. The following is example JSON for
        ``additionalModelResponseFieldPaths``.

        ``[ "/stop_sequence" ]``

        For information about the JSON Pointer syntax, see the `Internet Engineering Task Force (IETF) <https://datatracker.ietf.org/doc/html/rfc6901>`_
        documentation.

        ``Converse`` and ``ConverseStream`` reject an empty JSON Pointer or incorrectly
        structured JSON Pointer with a ``400`` error code. if the JSON Pointer is valid,
        but the requested field is not in the model response, it is ignored by
        ``Converse``.

    :param request_metadata:
        Key-value pairs that you can use to filter invocation logs.

    :param performance_config:
        Model performance settings for the request.

    """

    model_id: str | None = None
    messages: list[Message] | None = None
    system: list[SystemContentBlock] | None = None
    inference_config: InferenceConfiguration | None = None
    tool_config: ToolConfiguration | None = None
    guardrail_config: GuardrailConfiguration | None = None
    additional_model_request_fields: Document | None = None
    prompt_variables: dict[str, PromptVariableValues] | None = field(
        repr=False, default=None
    )
    additional_model_response_field_paths: list[str] | None = None
    request_metadata: dict[str, str] | None = field(repr=False, default=None)
    performance_config: PerformanceConfiguration | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.messages is not None:
            _serialize_messages(
                serializer, _SCHEMA_CONVERSE_INPUT.members["messages"], self.messages
            )

        if self.system is not None:
            _serialize_system_content_blocks(
                serializer, _SCHEMA_CONVERSE_INPUT.members["system"], self.system
            )

        if self.inference_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_INPUT.members["inferenceConfig"], self.inference_config
            )

        if self.tool_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_INPUT.members["toolConfig"], self.tool_config
            )

        if self.guardrail_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_INPUT.members["guardrailConfig"], self.guardrail_config
            )

        if self.additional_model_request_fields is not None:
            serializer.write_document(
                _SCHEMA_CONVERSE_INPUT.members["additionalModelRequestFields"],
                self.additional_model_request_fields,
            )

        if self.prompt_variables is not None:
            _serialize_prompt_variable_map(
                serializer,
                _SCHEMA_CONVERSE_INPUT.members["promptVariables"],
                self.prompt_variables,
            )

        if self.additional_model_response_field_paths is not None:
            _serialize_additional_model_response_field_paths(
                serializer,
                _SCHEMA_CONVERSE_INPUT.members["additionalModelResponseFieldPaths"],
                self.additional_model_response_field_paths,
            )

        if self.request_metadata is not None:
            _serialize_request_metadata(
                serializer,
                _SCHEMA_CONVERSE_INPUT.members["requestMetadata"],
                self.request_metadata,
            )

        if self.performance_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_INPUT.members["performanceConfig"],
                self.performance_config,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_CONVERSE_INPUT.members["modelId"]
                    )

                case 1:
                    kwargs["messages"] = _deserialize_messages(
                        de, _SCHEMA_CONVERSE_INPUT.members["messages"]
                    )

                case 2:
                    kwargs["system"] = _deserialize_system_content_blocks(
                        de, _SCHEMA_CONVERSE_INPUT.members["system"]
                    )

                case 3:
                    kwargs["inference_config"] = InferenceConfiguration.deserialize(de)

                case 4:
                    kwargs["tool_config"] = ToolConfiguration.deserialize(de)

                case 5:
                    kwargs["guardrail_config"] = GuardrailConfiguration.deserialize(de)

                case 6:
                    kwargs["additional_model_request_fields"] = de.read_document(
                        _SCHEMA_CONVERSE_INPUT.members["additionalModelRequestFields"]
                    )

                case 7:
                    kwargs["prompt_variables"] = _deserialize_prompt_variable_map(
                        de, _SCHEMA_CONVERSE_INPUT.members["promptVariables"]
                    )

                case 8:
                    kwargs["additional_model_response_field_paths"] = (
                        _deserialize_additional_model_response_field_paths(
                            de,
                            _SCHEMA_CONVERSE_INPUT.members[
                                "additionalModelResponseFieldPaths"
                            ],
                        )
                    )

                case 9:
                    kwargs["request_metadata"] = _deserialize_request_metadata(
                        de, _SCHEMA_CONVERSE_INPUT.members["requestMetadata"]
                    )

                case 10:
                    kwargs["performance_config"] = PerformanceConfiguration.deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_INPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseMetrics:
    """
    Metrics for a call to `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
    .

    :param latency_ms:
        **[Required]** - The latency of the call to ``Converse``, in milliseconds.

    """

    latency_ms: int

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_METRICS, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_long(
            _SCHEMA_CONVERSE_METRICS.members["latencyMs"], self.latency_ms
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["latency_ms"] = de.read_long(
                        _SCHEMA_CONVERSE_METRICS.members["latencyMs"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_METRICS, consumer=_consumer)
        return kwargs


@dataclass
class ConverseOutputMessage:
    """
    The message that the model generates.

    """

    value: Message

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_OUTPUT.members["message"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=Message.deserialize(deserializer))


@dataclass
class ConverseOutputUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ConverseOutput = Union[ConverseOutputMessage | ConverseOutputUnknown]

"""
The output from a call to `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_
.

"""


class _ConverseOutputDeserializer:
    _result: ConverseOutput | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ConverseOutput:
        self._result = None
        deserializer.read_struct(_SCHEMA_CONVERSE_OUTPUT, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ConverseOutputMessage.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ConverseOutput) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


class StopReason(StrEnum):
    END_TURN = "end_turn"
    TOOL_USE = "tool_use"
    MAX_TOKENS = "max_tokens"
    STOP_SEQUENCE = "stop_sequence"
    GUARDRAIL_INTERVENED = "guardrail_intervened"
    CONTENT_FILTERED = "content_filtered"


def _serialize_guardrail_assessment_map(
    serializer: ShapeSerializer, schema: Schema, value: dict[str, GuardrailAssessment]
) -> None:
    with serializer.begin_map(schema, len(value)) as m:
        value_schema = schema.members["value"]
        for k, v in value.items():
            m.entry(k, lambda vs: vs.write_struct(value_schema, v))


def _deserialize_guardrail_assessment_map(
    deserializer: ShapeDeserializer, schema: Schema
) -> dict[str, GuardrailAssessment]:
    result: dict[str, GuardrailAssessment] = {}
    value_schema = schema.members["value"]

    def _read_value(k: str, d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result[k] = GuardrailAssessment.deserialize(d)

    deserializer.read_map(schema, _read_value)
    return result


def _serialize_model_outputs(
    serializer: ShapeSerializer, schema: Schema, value: list[str]
) -> None:
    member_schema = schema.members["member"]
    with serializer.begin_list(schema, len(value)) as ls:
        for e in value:
            ls.write_string(member_schema, e)


def _deserialize_model_outputs(
    deserializer: ShapeDeserializer, schema: Schema
) -> list[str]:
    result: list[str] = []
    member_schema = schema.members["member"]

    def _read_value(d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result.append(d.read_string(member_schema))

    deserializer.read_list(schema, _read_value)
    return result


def _serialize_guardrail_assessment_list_map(
    serializer: ShapeSerializer,
    schema: Schema,
    value: dict[str, list[GuardrailAssessment]],
) -> None:
    with serializer.begin_map(schema, len(value)) as m:
        value_schema = schema.members["value"]
        for k, v in value.items():
            m.entry(
                k, lambda vs: _serialize_guardrail_assessment_list(vs, value_schema, v)
            )


def _deserialize_guardrail_assessment_list_map(
    deserializer: ShapeDeserializer, schema: Schema
) -> dict[str, list[GuardrailAssessment]]:
    result: dict[str, list[GuardrailAssessment]] = {}
    value_schema = schema.members["value"]

    def _read_value(k: str, d: ShapeDeserializer):
        if d.is_null():
            d.read_null()

        else:
            result[k] = _deserialize_guardrail_assessment_list(d, value_schema)

    deserializer.read_map(schema, _read_value)
    return result


@dataclass(kw_only=True)
class GuardrailTraceAssessment:
    """
    A Top level guardrail trace object. For more information, see `ConverseTrace`.

    :param model_output:
        The output from the model.

    :param input_assessment:
        The input assessment.

    :param output_assessments:
        the output assessments.

    :param action_reason:
        Provides the reason for the action taken when harmful content is detected.

    """

    model_output: list[str] | None = None
    input_assessment: dict[str, GuardrailAssessment] | None = None
    output_assessments: dict[str, list[GuardrailAssessment]] | None = None
    action_reason: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_TRACE_ASSESSMENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.model_output is not None:
            _serialize_model_outputs(
                serializer,
                _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["modelOutput"],
                self.model_output,
            )

        if self.input_assessment is not None:
            _serialize_guardrail_assessment_map(
                serializer,
                _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["inputAssessment"],
                self.input_assessment,
            )

        if self.output_assessments is not None:
            _serialize_guardrail_assessment_list_map(
                serializer,
                _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["outputAssessments"],
                self.output_assessments,
            )

        if self.action_reason is not None:
            serializer.write_string(
                _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["actionReason"],
                self.action_reason,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["model_output"] = _deserialize_model_outputs(
                        de, _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["modelOutput"]
                    )

                case 1:
                    kwargs["input_assessment"] = _deserialize_guardrail_assessment_map(
                        de,
                        _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["inputAssessment"],
                    )

                case 2:
                    kwargs["output_assessments"] = (
                        _deserialize_guardrail_assessment_list_map(
                            de,
                            _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members[
                                "outputAssessments"
                            ],
                        )
                    )

                case 3:
                    kwargs["action_reason"] = de.read_string(
                        _SCHEMA_GUARDRAIL_TRACE_ASSESSMENT.members["actionReason"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_GUARDRAIL_TRACE_ASSESSMENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class PromptRouterTrace:
    """
    A prompt router trace.

    :param invoked_model_id:
        The ID of the invoked model.

    """

    invoked_model_id: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_PROMPT_ROUTER_TRACE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.invoked_model_id is not None:
            serializer.write_string(
                _SCHEMA_PROMPT_ROUTER_TRACE.members["invokedModelId"],
                self.invoked_model_id,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["invoked_model_id"] = de.read_string(
                        _SCHEMA_PROMPT_ROUTER_TRACE.members["invokedModelId"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_PROMPT_ROUTER_TRACE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseTrace:
    """
    The trace object in a response from `Converse <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html>`_.
    Currently, you can only trace guardrails.

    :param guardrail:
        The guardrail trace object.

    :param prompt_router:
        The request's prompt router.

    """

    guardrail: GuardrailTraceAssessment | None = None
    prompt_router: PromptRouterTrace | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_TRACE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.guardrail is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_TRACE.members["guardrail"], self.guardrail
            )

        if self.prompt_router is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_TRACE.members["promptRouter"], self.prompt_router
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail"] = GuardrailTraceAssessment.deserialize(de)

                case 1:
                    kwargs["prompt_router"] = PromptRouterTrace.deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_TRACE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class TokenUsage:
    """
    The tokens used in a message API inference call.

    :param input_tokens:
        **[Required]** - The number of tokens sent in the request to the model.

    :param output_tokens:
        **[Required]** - The number of tokens that the model generated for the request.

    :param total_tokens:
        **[Required]** - The total of input tokens and tokens generated by the model.

    :param cache_read_input_tokens:
        The number of input tokens read from the cache for the request.

    :param cache_write_input_tokens:
        The number of input tokens written to the cache for the request.

    """

    input_tokens: int

    output_tokens: int

    total_tokens: int

    cache_read_input_tokens: int | None = None
    cache_write_input_tokens: int | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOKEN_USAGE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_integer(
            _SCHEMA_TOKEN_USAGE.members["inputTokens"], self.input_tokens
        )
        serializer.write_integer(
            _SCHEMA_TOKEN_USAGE.members["outputTokens"], self.output_tokens
        )
        serializer.write_integer(
            _SCHEMA_TOKEN_USAGE.members["totalTokens"], self.total_tokens
        )
        if self.cache_read_input_tokens is not None:
            serializer.write_integer(
                _SCHEMA_TOKEN_USAGE.members["cacheReadInputTokens"],
                self.cache_read_input_tokens,
            )

        if self.cache_write_input_tokens is not None:
            serializer.write_integer(
                _SCHEMA_TOKEN_USAGE.members["cacheWriteInputTokens"],
                self.cache_write_input_tokens,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["input_tokens"] = de.read_integer(
                        _SCHEMA_TOKEN_USAGE.members["inputTokens"]
                    )

                case 1:
                    kwargs["output_tokens"] = de.read_integer(
                        _SCHEMA_TOKEN_USAGE.members["outputTokens"]
                    )

                case 2:
                    kwargs["total_tokens"] = de.read_integer(
                        _SCHEMA_TOKEN_USAGE.members["totalTokens"]
                    )

                case 3:
                    kwargs["cache_read_input_tokens"] = de.read_integer(
                        _SCHEMA_TOKEN_USAGE.members["cacheReadInputTokens"]
                    )

                case 4:
                    kwargs["cache_write_input_tokens"] = de.read_integer(
                        _SCHEMA_TOKEN_USAGE.members["cacheWriteInputTokens"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOKEN_USAGE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseOperationOutput:
    """

    :param output:
        **[Required]** - The result from the call to ``Converse``.

    :param stop_reason:
        **[Required]** - The reason why the model stopped generating output.

    :param usage:
        **[Required]** - The total number of tokens used in the call to ``Converse``.
        The total includes the tokens input to the model and the tokens generated by the
        model.

    :param metrics:
        **[Required]** - Metrics for the call to ``Converse``.

    :param additional_model_response_fields:
        Additional fields in the response that are unique to the model.

    :param trace:
        A trace object that contains information about the Guardrail behavior.

    :param performance_config:
        Model performance settings for the request.

    """

    output: ConverseOutput

    stop_reason: str

    usage: TokenUsage

    metrics: ConverseMetrics

    additional_model_response_fields: Document | None = None
    trace: ConverseTrace | None = None
    performance_config: PerformanceConfiguration | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_OPERATION_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["output"], self.output
        )
        serializer.write_string(
            _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["stopReason"], self.stop_reason
        )
        serializer.write_struct(
            _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["usage"], self.usage
        )
        serializer.write_struct(
            _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["metrics"], self.metrics
        )
        if self.additional_model_response_fields is not None:
            serializer.write_document(
                _SCHEMA_CONVERSE_OPERATION_OUTPUT.members[
                    "additionalModelResponseFields"
                ],
                self.additional_model_response_fields,
            )

        if self.trace is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["trace"], self.trace
            )

        if self.performance_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["performanceConfig"],
                self.performance_config,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["output"] = _ConverseOutputDeserializer().deserialize(de)

                case 1:
                    kwargs["stop_reason"] = de.read_string(
                        _SCHEMA_CONVERSE_OPERATION_OUTPUT.members["stopReason"]
                    )

                case 2:
                    kwargs["usage"] = TokenUsage.deserialize(de)

                case 3:
                    kwargs["metrics"] = ConverseMetrics.deserialize(de)

                case 4:
                    kwargs["additional_model_response_fields"] = de.read_document(
                        _SCHEMA_CONVERSE_OPERATION_OUTPUT.members[
                            "additionalModelResponseFields"
                        ]
                    )

                case 5:
                    kwargs["trace"] = ConverseTrace.deserialize(de)

                case 6:
                    kwargs["performance_config"] = PerformanceConfiguration.deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_OPERATION_OUTPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ModelErrorException(ApiError):
    """
    The request failed due to an error while processing the model.

    :param message: A message associated with the specific error.

    :param original_status_code:
        The original status code.

    :param resource_name:
        The resource name.

    """

    code: ClassVar[str] = "ModelErrorException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str
    original_status_code: int | None = None
    resource_name: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MODEL_ERROR_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_MODEL_ERROR_EXCEPTION.members["message"], self.message
            )

        if self.original_status_code is not None:
            serializer.write_integer(
                _SCHEMA_MODEL_ERROR_EXCEPTION.members["originalStatusCode"],
                self.original_status_code,
            )

        if self.resource_name is not None:
            serializer.write_string(
                _SCHEMA_MODEL_ERROR_EXCEPTION.members["resourceName"],
                self.resource_name,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_MODEL_ERROR_EXCEPTION.members["message"]
                    )

                case 1:
                    kwargs["original_status_code"] = de.read_integer(
                        _SCHEMA_MODEL_ERROR_EXCEPTION.members["originalStatusCode"]
                    )

                case 2:
                    kwargs["resource_name"] = de.read_string(
                        _SCHEMA_MODEL_ERROR_EXCEPTION.members["resourceName"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MODEL_ERROR_EXCEPTION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ModelNotReadyException(ApiError):
    """
    The model specified in the request is not ready to serve inference requests. The
    AWS SDK will automatically retry the operation up to 5 times. For information
    about configuring automatic retries, see `Retry behavior <https://docs.aws.amazon.com/sdkref/latest/guide/feature-retry-behavior.html>`_
    in the *AWS SDKs and Tools* reference guide.

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ModelNotReadyException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MODEL_NOT_READY_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_MODEL_NOT_READY_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_MODEL_NOT_READY_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MODEL_NOT_READY_EXCEPTION, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ModelTimeoutException(ApiError):
    """
    The request took too long to process. Processing time exceeded the model timeout
    length.

    :param message: A message associated with the specific error.

    """

    code: ClassVar[str] = "ModelTimeoutException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MODEL_TIMEOUT_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_MODEL_TIMEOUT_EXCEPTION.members["message"], self.message
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_MODEL_TIMEOUT_EXCEPTION.members["message"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MODEL_TIMEOUT_EXCEPTION, consumer=_consumer)
        return kwargs


CONVERSE = APIOperation(
    input=ConverseInput,
    output=ConverseOperationOutput,
    schema=_SCHEMA_CONVERSE,
    input_schema=_SCHEMA_CONVERSE_INPUT,
    output_schema=_SCHEMA_CONVERSE_OPERATION_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelErrorException"
            ): ModelErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelNotReadyException"
            ): ModelNotReadyException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelTimeoutException"
            ): ModelTimeoutException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


class GuardrailStreamProcessingMode(StrEnum):
    SYNC = "sync"
    ASYNC_ = "async"


@dataclass(kw_only=True)
class GuardrailStreamConfiguration:
    """
    Configuration information for a guardrail that you use with the `ConverseStream`
    action.

    :param guardrail_identifier:
        **[Required]** - The identifier for the guardrail.

    :param guardrail_version:
        **[Required]** - The version of the guardrail.

    :param trace:
        The trace behavior for the guardrail.

    :param stream_processing_mode:
        The processing mode.

        The processing mode. For more information, see *Configure streaming response
        behavior* in the *Amazon Bedrock User Guide*.

    """

    guardrail_identifier: str

    guardrail_version: str

    trace: str = "disabled"
    stream_processing_mode: str = "sync"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_GUARDRAIL_STREAM_CONFIGURATION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members["guardrailIdentifier"],
            self.guardrail_identifier,
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members["guardrailVersion"],
            self.guardrail_version,
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members["trace"], self.trace
        )
        serializer.write_string(
            _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members["streamProcessingMode"],
            self.stream_processing_mode,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail_identifier"] = de.read_string(
                        _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members[
                            "guardrailIdentifier"
                        ]
                    )

                case 1:
                    kwargs["guardrail_version"] = de.read_string(
                        _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members[
                            "guardrailVersion"
                        ]
                    )

                case 2:
                    kwargs["trace"] = de.read_string(
                        _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members["trace"]
                    )

                case 3:
                    kwargs["stream_processing_mode"] = de.read_string(
                        _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION.members[
                            "streamProcessingMode"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_GUARDRAIL_STREAM_CONFIGURATION, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class ConverseStreamInput:
    """

    :param model_id:
        **[Required]** - Specifies the model or throughput with which to run inference,
        or the prompt resource to use in inference. The value depends on the resource
        that you use:

        * If you use a base model, specify the model ID or its ARN. For a list of model
          IDs for base models, see `Amazon Bedrock base model IDs (on-demand throughput) <https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns>`_
          in the Amazon Bedrock User Guide.

        * If you use an inference profile, specify the inference profile ID or its ARN.
          For a list of inference profile IDs, see `Supported Regions and models for cross-region inference <https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a provisioned model, specify the ARN of the Provisioned Throughput.
          For more information, see `Run inference using a Provisioned Throughput <https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a custom model, first purchase Provisioned Throughput for it. Then
          specify the ARN of the resulting provisioned model. For more information, see
          `Use a custom model in Amazon Bedrock <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html>`_
          in the Amazon Bedrock User Guide.

        * To include a prompt that was defined in `Prompt management <https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management.html>`_,
          specify the ARN of the prompt version to use.

        The Converse API doesn't support `imported models <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html>`_
        .

    :param messages:
        The messages that you want to send to the model.

    :param system:
        A prompt that provides instructions or context to the model about the task it
        should perform, or the persona it should adopt during the conversation.

    :param inference_config:
        Inference parameters to pass to the model. ``Converse`` and ``ConverseStream``
        support a base set of inference parameters. If you need to pass additional
        parameters that the model supports, use the ``additionalModelRequestFields``
        request field.

    :param tool_config:
        Configuration information for the tools that the model can use when generating a
        response.

        For information about models that support streaming tool use, see `Supported models and model features <https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html#conversation-inference-supported-models-features>`_
        .

    :param guardrail_config:
        Configuration information for a guardrail that you want to use in the request.
        If you include ``guardContent`` blocks in the ``content`` field in the
        ``messages`` field, the guardrail operates only on those messages. If you
        include no ``guardContent`` blocks, the guardrail operates on all messages in
        the request body and in any included prompt resource.

    :param additional_model_request_fields:
        Additional inference parameters that the model supports, beyond the base set of
        inference parameters that ``Converse`` and ``ConverseStream`` support in the ``inferenceConfig`` field. For more information, see `Model parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param prompt_variables:
        Contains a map of variables in a prompt from Prompt management to objects
        containing the values to fill in for them when running model invocation. This
        field is ignored if you don't specify a prompt resource in the ``modelId``
        field.

    :param additional_model_response_field_paths:
        Additional model parameters field paths to return in the response. ``Converse``
        and ``ConverseStream`` return the requested fields as a JSON Pointer object in
        the ``additionalModelResponseFields`` field. The following is example JSON for
        ``additionalModelResponseFieldPaths``.

        ``[ "/stop_sequence" ]``

        For information about the JSON Pointer syntax, see the `Internet Engineering Task Force (IETF) <https://datatracker.ietf.org/doc/html/rfc6901>`_
        documentation.

        ``Converse`` and ``ConverseStream`` reject an empty JSON Pointer or incorrectly
        structured JSON Pointer with a ``400`` error code. if the JSON Pointer is valid,
        but the requested field is not in the model response, it is ignored by
        ``Converse``.

    :param request_metadata:
        Key-value pairs that you can use to filter invocation logs.

    :param performance_config:
        Model performance settings for the request.

    """

    model_id: str | None = None
    messages: list[Message] | None = None
    system: list[SystemContentBlock] | None = None
    inference_config: InferenceConfiguration | None = None
    tool_config: ToolConfiguration | None = None
    guardrail_config: GuardrailStreamConfiguration | None = None
    additional_model_request_fields: Document | None = None
    prompt_variables: dict[str, PromptVariableValues] | None = field(
        repr=False, default=None
    )
    additional_model_response_field_paths: list[str] | None = None
    request_metadata: dict[str, str] | None = field(repr=False, default=None)
    performance_config: PerformanceConfiguration | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.messages is not None:
            _serialize_messages(
                serializer,
                _SCHEMA_CONVERSE_STREAM_INPUT.members["messages"],
                self.messages,
            )

        if self.system is not None:
            _serialize_system_content_blocks(
                serializer, _SCHEMA_CONVERSE_STREAM_INPUT.members["system"], self.system
            )

        if self.inference_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_INPUT.members["inferenceConfig"],
                self.inference_config,
            )

        if self.tool_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_INPUT.members["toolConfig"], self.tool_config
            )

        if self.guardrail_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_INPUT.members["guardrailConfig"],
                self.guardrail_config,
            )

        if self.additional_model_request_fields is not None:
            serializer.write_document(
                _SCHEMA_CONVERSE_STREAM_INPUT.members["additionalModelRequestFields"],
                self.additional_model_request_fields,
            )

        if self.prompt_variables is not None:
            _serialize_prompt_variable_map(
                serializer,
                _SCHEMA_CONVERSE_STREAM_INPUT.members["promptVariables"],
                self.prompt_variables,
            )

        if self.additional_model_response_field_paths is not None:
            _serialize_additional_model_response_field_paths(
                serializer,
                _SCHEMA_CONVERSE_STREAM_INPUT.members[
                    "additionalModelResponseFieldPaths"
                ],
                self.additional_model_response_field_paths,
            )

        if self.request_metadata is not None:
            _serialize_request_metadata(
                serializer,
                _SCHEMA_CONVERSE_STREAM_INPUT.members["requestMetadata"],
                self.request_metadata,
            )

        if self.performance_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_INPUT.members["performanceConfig"],
                self.performance_config,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_CONVERSE_STREAM_INPUT.members["modelId"]
                    )

                case 1:
                    kwargs["messages"] = _deserialize_messages(
                        de, _SCHEMA_CONVERSE_STREAM_INPUT.members["messages"]
                    )

                case 2:
                    kwargs["system"] = _deserialize_system_content_blocks(
                        de, _SCHEMA_CONVERSE_STREAM_INPUT.members["system"]
                    )

                case 3:
                    kwargs["inference_config"] = InferenceConfiguration.deserialize(de)

                case 4:
                    kwargs["tool_config"] = ToolConfiguration.deserialize(de)

                case 5:
                    kwargs["guardrail_config"] = (
                        GuardrailStreamConfiguration.deserialize(de)
                    )

                case 6:
                    kwargs["additional_model_request_fields"] = de.read_document(
                        _SCHEMA_CONVERSE_STREAM_INPUT.members[
                            "additionalModelRequestFields"
                        ]
                    )

                case 7:
                    kwargs["prompt_variables"] = _deserialize_prompt_variable_map(
                        de, _SCHEMA_CONVERSE_STREAM_INPUT.members["promptVariables"]
                    )

                case 8:
                    kwargs["additional_model_response_field_paths"] = (
                        _deserialize_additional_model_response_field_paths(
                            de,
                            _SCHEMA_CONVERSE_STREAM_INPUT.members[
                                "additionalModelResponseFieldPaths"
                            ],
                        )
                    )

                case 9:
                    kwargs["request_metadata"] = _deserialize_request_metadata(
                        de, _SCHEMA_CONVERSE_STREAM_INPUT.members["requestMetadata"]
                    )

                case 10:
                    kwargs["performance_config"] = PerformanceConfiguration.deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_STREAM_INPUT, consumer=_consumer)
        return kwargs


@dataclass
class ReasoningContentBlockDeltaText:
    """
    The reasoning that the model used to return the output.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["text"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(
                _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["text"]
            )
        )


@dataclass
class ReasoningContentBlockDeltaRedactedContent:
    """
    The content in the reasoning that was encrypted by the model provider for safety
    reasons. The encryption doesn't affect the quality of responses.

    """

    value: bytes

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_blob(
            _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["redactedContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_blob(
                _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["redactedContent"]
            )
        )


@dataclass
class ReasoningContentBlockDeltaSignature:
    """
    A token that verifies that the reasoning text was generated by the model. If you
    pass a reasoning block back to the API in a multi-turn conversation, include the
    text and its signature unmodified.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_REASONING_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["signature"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(
                _SCHEMA_REASONING_CONTENT_BLOCK_DELTA.members["signature"]
            )
        )


@dataclass
class ReasoningContentBlockDeltaUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ReasoningContentBlockDelta = Union[
    ReasoningContentBlockDeltaText
    | ReasoningContentBlockDeltaRedactedContent
    | ReasoningContentBlockDeltaSignature
    | ReasoningContentBlockDeltaUnknown
]

"""
Contains content regarding the reasoning that is carried out by the model with
respect to the content in the content block. Reasoning refers to a Chain of
Thought (CoT) that the model generates to enhance the accuracy of its final
response.

"""


class _ReasoningContentBlockDeltaDeserializer:
    _result: ReasoningContentBlockDelta | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> ReasoningContentBlockDelta:
        self._result = None
        deserializer.read_struct(_SCHEMA_REASONING_CONTENT_BLOCK_DELTA, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ReasoningContentBlockDeltaText.deserialize(de))

            case 1:
                self._set_result(
                    ReasoningContentBlockDeltaRedactedContent.deserialize(de)
                )

            case 2:
                self._set_result(ReasoningContentBlockDeltaSignature.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ReasoningContentBlockDelta) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ToolUseBlockDelta:
    """
    The delta for a tool use block.

    :param input:
        **[Required]** - The input for a requested tool.

    """

    input: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_USE_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_TOOL_USE_BLOCK_DELTA.members["input"], self.input
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["input"] = de.read_string(
                        _SCHEMA_TOOL_USE_BLOCK_DELTA.members["input"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_USE_BLOCK_DELTA, consumer=_consumer)
        return kwargs


@dataclass
class ContentBlockDeltaText:
    """
    The content text.

    """

    value: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_CONTENT_BLOCK_DELTA.members["text"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=deserializer.read_string(_SCHEMA_CONTENT_BLOCK_DELTA.members["text"])
        )


@dataclass
class ContentBlockDeltaToolUse:
    """
    Information about a tool that the model is requesting to use.

    """

    value: ToolUseBlockDelta

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK_DELTA.members["toolUse"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ToolUseBlockDelta.deserialize(deserializer))


@dataclass
class ContentBlockDeltaReasoningContent:
    """
    Contains content regarding the reasoning that is carried out by the model.
    Reasoning refers to a Chain of Thought (CoT) that the model generates to enhance
    the accuracy of its final response.

    """

    value: ReasoningContentBlockDelta

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_DELTA, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK_DELTA.members["reasoningContent"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(
            value=_ReasoningContentBlockDeltaDeserializer().deserialize(deserializer)
        )


@dataclass
class ContentBlockDeltaUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ContentBlockDelta = Union[
    ContentBlockDeltaText
    | ContentBlockDeltaToolUse
    | ContentBlockDeltaReasoningContent
    | ContentBlockDeltaUnknown
]

"""
A block of content in a streaming response.

"""


class _ContentBlockDeltaDeserializer:
    _result: ContentBlockDelta | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ContentBlockDelta:
        self._result = None
        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK_DELTA, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ContentBlockDeltaText.deserialize(de))

            case 1:
                self._set_result(ContentBlockDeltaToolUse.deserialize(de))

            case 2:
                self._set_result(ContentBlockDeltaReasoningContent.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ContentBlockDelta) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ContentBlockDeltaEvent:
    """
    The content block delta event.

    :param delta:
        **[Required]** - The delta for a content block delta event.

    :param content_block_index:
        **[Required]** - The block index for a content block delta event.

    """

    delta: ContentBlockDelta

    content_block_index: int

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_DELTA_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK_DELTA_EVENT.members["delta"], self.delta
        )
        serializer.write_integer(
            _SCHEMA_CONTENT_BLOCK_DELTA_EVENT.members["contentBlockIndex"],
            self.content_block_index,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["delta"] = _ContentBlockDeltaDeserializer().deserialize(de)

                case 1:
                    kwargs["content_block_index"] = de.read_integer(
                        _SCHEMA_CONTENT_BLOCK_DELTA_EVENT.members["contentBlockIndex"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK_DELTA_EVENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ToolUseBlockStart:
    """
    The start of a tool use block.

    :param tool_use_id:
        **[Required]** - The ID for the tool request.

    :param name:
        **[Required]** - The name of the tool that the model is requesting to use.

    """

    tool_use_id: str

    name: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_TOOL_USE_BLOCK_START, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_TOOL_USE_BLOCK_START.members["toolUseId"], self.tool_use_id
        )
        serializer.write_string(_SCHEMA_TOOL_USE_BLOCK_START.members["name"], self.name)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["tool_use_id"] = de.read_string(
                        _SCHEMA_TOOL_USE_BLOCK_START.members["toolUseId"]
                    )

                case 1:
                    kwargs["name"] = de.read_string(
                        _SCHEMA_TOOL_USE_BLOCK_START.members["name"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_TOOL_USE_BLOCK_START, consumer=_consumer)
        return kwargs


@dataclass
class ContentBlockStartToolUse:
    """
    Information about a tool that the model is requesting to use.

    """

    value: ToolUseBlockStart

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_START, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK_START.members["toolUse"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ToolUseBlockStart.deserialize(deserializer))


@dataclass
class ContentBlockStartUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ContentBlockStart = Union[ContentBlockStartToolUse | ContentBlockStartUnknown]

"""
Content block start information.

"""


class _ContentBlockStartDeserializer:
    _result: ContentBlockStart | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ContentBlockStart:
        self._result = None
        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK_START, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ContentBlockStartToolUse.deserialize(de))

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ContentBlockStart) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ContentBlockStartEvent:
    """
    Content block start event.

    :param start:
        **[Required]** - Start information about a content block start event.

    :param content_block_index:
        **[Required]** - The index for a content block start event.

    """

    start: ContentBlockStart

    content_block_index: int

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_START_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONTENT_BLOCK_START_EVENT.members["start"], self.start
        )
        serializer.write_integer(
            _SCHEMA_CONTENT_BLOCK_START_EVENT.members["contentBlockIndex"],
            self.content_block_index,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["start"] = _ContentBlockStartDeserializer().deserialize(de)

                case 1:
                    kwargs["content_block_index"] = de.read_integer(
                        _SCHEMA_CONTENT_BLOCK_START_EVENT.members["contentBlockIndex"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK_START_EVENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ContentBlockStopEvent:
    """
    A content block stop event.

    :param content_block_index:
        **[Required]** - The index for a content block.

    """

    content_block_index: int

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONTENT_BLOCK_STOP_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_integer(
            _SCHEMA_CONTENT_BLOCK_STOP_EVENT.members["contentBlockIndex"],
            self.content_block_index,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["content_block_index"] = de.read_integer(
                        _SCHEMA_CONTENT_BLOCK_STOP_EVENT.members["contentBlockIndex"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONTENT_BLOCK_STOP_EVENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class MessageStartEvent:
    """
    The start of a message.

    :param role:
        **[Required]** - The role for the message.

    """

    role: str

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MESSAGE_START_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(_SCHEMA_MESSAGE_START_EVENT.members["role"], self.role)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["role"] = de.read_string(
                        _SCHEMA_MESSAGE_START_EVENT.members["role"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MESSAGE_START_EVENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class MessageStopEvent:
    """
    The stop event for a message.

    :param stop_reason:
        **[Required]** - The reason why the model stopped generating output.

    :param additional_model_response_fields:
        The additional model response fields.

    """

    stop_reason: str

    additional_model_response_fields: Document | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MESSAGE_STOP_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_string(
            _SCHEMA_MESSAGE_STOP_EVENT.members["stopReason"], self.stop_reason
        )
        if self.additional_model_response_fields is not None:
            serializer.write_document(
                _SCHEMA_MESSAGE_STOP_EVENT.members["additionalModelResponseFields"],
                self.additional_model_response_fields,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["stop_reason"] = de.read_string(
                        _SCHEMA_MESSAGE_STOP_EVENT.members["stopReason"]
                    )

                case 1:
                    kwargs["additional_model_response_fields"] = de.read_document(
                        _SCHEMA_MESSAGE_STOP_EVENT.members[
                            "additionalModelResponseFields"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_MESSAGE_STOP_EVENT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseStreamMetrics:
    """
    Metrics for the stream.

    :param latency_ms:
        **[Required]** - The latency for the streaming request, in milliseconds.

    """

    latency_ms: int

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_METRICS, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_long(
            _SCHEMA_CONVERSE_STREAM_METRICS.members["latencyMs"], self.latency_ms
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["latency_ms"] = de.read_long(
                        _SCHEMA_CONVERSE_STREAM_METRICS.members["latencyMs"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_STREAM_METRICS, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseStreamTrace:
    """
    The trace object in a response from `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_.
    Currently, you can only trace guardrails.

    :param guardrail:
        The guardrail trace object.

    :param prompt_router:
        The request's prompt router.

    """

    guardrail: GuardrailTraceAssessment | None = None
    prompt_router: PromptRouterTrace | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_TRACE, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.guardrail is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_TRACE.members["guardrail"], self.guardrail
            )

        if self.prompt_router is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_TRACE.members["promptRouter"],
                self.prompt_router,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["guardrail"] = GuardrailTraceAssessment.deserialize(de)

                case 1:
                    kwargs["prompt_router"] = PromptRouterTrace.deserialize(de)

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_CONVERSE_STREAM_TRACE, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class ConverseStreamMetadataEvent:
    """
    A conversation stream metadata event.

    :param usage:
        **[Required]** - Usage information for the conversation stream event.

    :param metrics:
        **[Required]** - The metrics for the conversation stream metadata event.

    :param trace:
        The trace object in the response from `ConverseStream <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ConverseStream.html>`_
        that contains information about the guardrail behavior.

    :param performance_config:
        Model performance configuration metadata for the conversation stream event.

    """

    usage: TokenUsage

    metrics: ConverseStreamMetrics

    trace: ConverseStreamTrace | None = None
    performance_config: PerformanceConfiguration | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_METADATA_EVENT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_METADATA_EVENT.members["usage"], self.usage
        )
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_METADATA_EVENT.members["metrics"], self.metrics
        )
        if self.trace is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_METADATA_EVENT.members["trace"], self.trace
            )

        if self.performance_config is not None:
            serializer.write_struct(
                _SCHEMA_CONVERSE_STREAM_METADATA_EVENT.members["performanceConfig"],
                self.performance_config,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["usage"] = TokenUsage.deserialize(de)

                case 1:
                    kwargs["metrics"] = ConverseStreamMetrics.deserialize(de)

                case 2:
                    kwargs["trace"] = ConverseStreamTrace.deserialize(de)

                case 3:
                    kwargs["performance_config"] = PerformanceConfiguration.deserialize(
                        de
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_CONVERSE_STREAM_METADATA_EVENT, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class ModelStreamErrorException(ApiError):
    """
    An error occurred while streaming the response. Retry your request.

    :param message: A message associated with the specific error.

    :param original_status_code:
        The original status code.

    :param original_message:
        The original message.

    """

    code: ClassVar[str] = "ModelStreamErrorException"
    fault: ClassVar[Literal["client", "server"]] = "client"

    message: str
    original_status_code: int | None = None
    original_message: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_MODEL_STREAM_ERROR_EXCEPTION, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.message is not None:
            serializer.write_string(
                _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members["message"], self.message
            )

        if self.original_status_code is not None:
            serializer.write_integer(
                _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members["originalStatusCode"],
                self.original_status_code,
            )

        if self.original_message is not None:
            serializer.write_string(
                _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members["originalMessage"],
                self.original_message,
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["message"] = de.read_string(
                        _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members["message"]
                    )

                case 1:
                    kwargs["original_status_code"] = de.read_integer(
                        _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members[
                            "originalStatusCode"
                        ]
                    )

                case 2:
                    kwargs["original_message"] = de.read_string(
                        _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION.members["originalMessage"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_MODEL_STREAM_ERROR_EXCEPTION, consumer=_consumer
        )
        return kwargs


@dataclass
class ConverseStreamOutputMessageStart:
    """
    Message start information.

    """

    value: MessageStartEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["messageStart"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=MessageStartEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputContentBlockStart:
    """
    Start information for a content block.

    """

    value: ContentBlockStartEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["contentBlockStart"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ContentBlockStartEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputContentBlockDelta:
    """
    The messages output content block delta.

    """

    value: ContentBlockDeltaEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["contentBlockDelta"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ContentBlockDeltaEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputContentBlockStop:
    """
    Stop information for a content block.

    """

    value: ContentBlockStopEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["contentBlockStop"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ContentBlockStopEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputMessageStop:
    """
    Message stop information.

    """

    value: MessageStopEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["messageStop"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=MessageStopEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputMetadata:
    """
    Metadata for the converse output stream.

    """

    value: ConverseStreamMetadataEvent

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["metadata"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ConverseStreamMetadataEvent.deserialize(deserializer))


@dataclass
class ConverseStreamOutputInternalServerException:
    """
    An internal server error occurred. Retry your request.

    """

    value: InternalServerException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["internalServerException"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=InternalServerException.deserialize(deserializer))


@dataclass
class ConverseStreamOutputModelStreamErrorException:
    """
    A streaming error occurred. Retry your request.

    """

    value: ModelStreamErrorException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["modelStreamErrorException"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ModelStreamErrorException.deserialize(deserializer))


@dataclass
class ConverseStreamOutputValidationException:
    """
    The input fails to satisfy the constraints specified by *Amazon Bedrock*. For
    troubleshooting this error, see `ValidationError <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-validation-error>`_
    in the Amazon Bedrock User Guide

    """

    value: ValidationException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["validationException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ValidationException.deserialize(deserializer))


@dataclass
class ConverseStreamOutputThrottlingException:
    """
    Your request was denied due to exceeding the account quotas for *Amazon
    Bedrock*. For troubleshooting this error, see `ThrottlingException <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-throttling-exception>`_
    in the Amazon Bedrock User Guide

    """

    value: ThrottlingException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["throttlingException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ThrottlingException.deserialize(deserializer))


@dataclass
class ConverseStreamOutputServiceUnavailableException:
    """
    The service isn't currently available. For troubleshooting this error, see
    `ServiceUnavailable <https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-service-unavailable>`_
    in the Amazon Bedrock User Guide

    """

    value: ServiceUnavailableException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_CONVERSE_STREAM_OUTPUT.members["serviceUnavailableException"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ServiceUnavailableException.deserialize(deserializer))


@dataclass
class ConverseStreamOutputUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ConverseStreamOutput = Union[
    ConverseStreamOutputMessageStart
    | ConverseStreamOutputContentBlockStart
    | ConverseStreamOutputContentBlockDelta
    | ConverseStreamOutputContentBlockStop
    | ConverseStreamOutputMessageStop
    | ConverseStreamOutputMetadata
    | ConverseStreamOutputInternalServerException
    | ConverseStreamOutputModelStreamErrorException
    | ConverseStreamOutputValidationException
    | ConverseStreamOutputThrottlingException
    | ConverseStreamOutputServiceUnavailableException
    | ConverseStreamOutputUnknown
]

"""
The messages output stream

"""


class _ConverseStreamOutputDeserializer:
    _result: ConverseStreamOutput | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ConverseStreamOutput:
        self._result = None
        deserializer.read_struct(_SCHEMA_CONVERSE_STREAM_OUTPUT, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ConverseStreamOutputMessageStart.deserialize(de))

            case 1:
                self._set_result(ConverseStreamOutputContentBlockStart.deserialize(de))

            case 2:
                self._set_result(ConverseStreamOutputContentBlockDelta.deserialize(de))

            case 3:
                self._set_result(ConverseStreamOutputContentBlockStop.deserialize(de))

            case 4:
                self._set_result(ConverseStreamOutputMessageStop.deserialize(de))

            case 5:
                self._set_result(ConverseStreamOutputMetadata.deserialize(de))

            case 6:
                self._set_result(
                    ConverseStreamOutputInternalServerException.deserialize(de)
                )

            case 7:
                self._set_result(
                    ConverseStreamOutputModelStreamErrorException.deserialize(de)
                )

            case 8:
                self._set_result(
                    ConverseStreamOutputValidationException.deserialize(de)
                )

            case 9:
                self._set_result(
                    ConverseStreamOutputThrottlingException.deserialize(de)
                )

            case 10:
                self._set_result(
                    ConverseStreamOutputServiceUnavailableException.deserialize(de)
                )

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ConverseStreamOutput) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class ConverseStreamOperationOutput:
    """ """

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_CONVERSE_STREAM_OPERATION_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_CONVERSE_STREAM_OPERATION_OUTPUT, consumer=_consumer
        )
        return kwargs


CONVERSE_STREAM = APIOperation(
    input=ConverseStreamInput,
    output=ConverseStreamOperationOutput,
    schema=_SCHEMA_CONVERSE_STREAM,
    input_schema=_SCHEMA_CONVERSE_STREAM_INPUT,
    output_schema=_SCHEMA_CONVERSE_STREAM_OPERATION_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelErrorException"
            ): ModelErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelNotReadyException"
            ): ModelNotReadyException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelTimeoutException"
            ): ModelTimeoutException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


class Trace(StrEnum):
    ENABLED = "ENABLED"
    DISABLED = "DISABLED"
    ENABLED_FULL = "ENABLED_FULL"


@dataclass(kw_only=True)
class InvokeModelInput:
    """

    :param body:
        The prompt and inference parameters in the format specified in the
        ``contentType`` in the header. You must provide the body in JSON format. To see the format and content of the request and response bodies for different models, refer to `Inference parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_.
        For more information, see `Run inference <https://docs.aws.amazon.com/bedrock/latest/userguide/api-methods-run.html>`_
        in the Bedrock User Guide.

    :param content_type:
        The MIME type of the input data in the request. You must specify
        ``application/json``.

    :param accept:
        The desired MIME type of the inference body in the response. The default value
        is ``application/json``.

    :param model_id:
        **[Required]** - The unique identifier of the model to invoke to run inference.

        The ``modelId`` to provide depends on the type of model or throughput that you
        use:

        * If you use a base model, specify the model ID or its ARN. For a list of model
          IDs for base models, see `Amazon Bedrock base model IDs (on-demand throughput) <https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns>`_
          in the Amazon Bedrock User Guide.

        * If you use an inference profile, specify the inference profile ID or its ARN.
          For a list of inference profile IDs, see `Supported Regions and models for cross-region inference <https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a provisioned model, specify the ARN of the Provisioned Throughput.
          For more information, see `Run inference using a Provisioned Throughput <https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a custom model, first purchase Provisioned Throughput for it. Then
          specify the ARN of the resulting provisioned model. For more information, see
          `Use a custom model in Amazon Bedrock <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use an `imported model <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html>`_,
          specify the ARN of the imported model. You can get the model ARN from a
          successful call to `CreateModelImportJob <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_CreateModelImportJob.html>`_
          or from the Imported models page in the Amazon Bedrock console.

    :param trace:
        Specifies whether to enable or disable the Bedrock trace. If enabled, you can
        see the full Bedrock trace.

    :param guardrail_identifier:
        The unique identifier of the guardrail that you want to use. If you don't
        provide a value, no guardrail is applied to the invocation.

        An error will be thrown in the following situations.

        * You don't provide a guardrail identifier but you specify the
          ``amazon-bedrock-guardrailConfig`` field in the request body.

        * You enable the guardrail but the ``contentType`` isn't ``application/json``.

        * You provide a guardrail identifier, but ``guardrailVersion`` isn't specified.

    :param guardrail_version:
        The version number for the guardrail. The value can also be ``DRAFT``.

    :param performance_config_latency:
        Model performance settings for the request.

    """

    body: bytes | None = field(repr=False, default=None)
    content_type: str | None = None
    accept: str | None = None
    model_id: str | None = None
    trace: str | None = None
    guardrail_identifier: str | None = None
    guardrail_version: str | None = None
    performance_config_latency: str = "standard"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INVOKE_MODEL_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["body"] = de.read_blob(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["body"]
                    )

                case 1:
                    kwargs["content_type"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["contentType"]
                    )

                case 2:
                    kwargs["accept"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["accept"]
                    )

                case 3:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["modelId"]
                    )

                case 4:
                    kwargs["trace"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["trace"]
                    )

                case 5:
                    kwargs["guardrail_identifier"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["guardrailIdentifier"]
                    )

                case 6:
                    kwargs["guardrail_version"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["guardrailVersion"]
                    )

                case 7:
                    kwargs["performance_config_latency"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_INPUT.members["performanceConfigLatency"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_INVOKE_MODEL_INPUT, consumer=_consumer)
        return kwargs


@dataclass(kw_only=True)
class InvokeModelOutput:
    """

    :param body:
        **[Required]** - Inference response from the model in the format specified in
        the ``contentType`` header. To see the format and content of the request and response bodies for different models, refer to `Inference parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_
        .

    :param content_type:
        **[Required]** - The MIME type of the inference result.

    :param performance_config_latency:
        Model performance settings for the request.

    """

    body: bytes = field(repr=False)

    content_type: str

    performance_config_latency: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INVOKE_MODEL_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["body"] = de.read_blob(
                        _SCHEMA_INVOKE_MODEL_OUTPUT.members["body"]
                    )

                case 1:
                    kwargs["content_type"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_OUTPUT.members["contentType"]
                    )

                case 2:
                    kwargs["performance_config_latency"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_OUTPUT.members["performanceConfigLatency"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_INVOKE_MODEL_OUTPUT, consumer=_consumer)
        return kwargs


INVOKE_MODEL = APIOperation(
    input=InvokeModelInput,
    output=InvokeModelOutput,
    schema=_SCHEMA_INVOKE_MODEL,
    input_schema=_SCHEMA_INVOKE_MODEL_INPUT,
    output_schema=_SCHEMA_INVOKE_MODEL_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelErrorException"
            ): ModelErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelNotReadyException"
            ): ModelNotReadyException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelTimeoutException"
            ): ModelTimeoutException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceQuotaExceededException"
            ): ServiceQuotaExceededException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


@dataclass(kw_only=True)
class BidirectionalInputPayloadPart:
    """
    Payload content for the bidirectional input. The input is an audio stream.

    :param bytes_:
        The audio content for the bidirectional input.

    """

    bytes_: bytes | None = field(repr=False, default=None)

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_BIDIRECTIONAL_INPUT_PAYLOAD_PART, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.bytes_ is not None:
            serializer.write_blob(
                _SCHEMA_BIDIRECTIONAL_INPUT_PAYLOAD_PART.members["bytes"], self.bytes_
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["bytes_"] = de.read_blob(
                        _SCHEMA_BIDIRECTIONAL_INPUT_PAYLOAD_PART.members["bytes"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_BIDIRECTIONAL_INPUT_PAYLOAD_PART, consumer=_consumer
        )
        return kwargs


@dataclass
class InvokeModelWithBidirectionalStreamInputChunk:
    """
    The audio chunk that is used as input for the invocation step.

    """

    value: BidirectionalInputPayloadPart

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_INPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_INPUT.members["chunk"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=BidirectionalInputPayloadPart.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamInputUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


InvokeModelWithBidirectionalStreamInput = Union[
    InvokeModelWithBidirectionalStreamInputChunk
    | InvokeModelWithBidirectionalStreamInputUnknown
]

"""
Payload content, the speech chunk, for the bidirectional input of the invocation
step.

"""


class _InvokeModelWithBidirectionalStreamInputDeserializer:
    _result: InvokeModelWithBidirectionalStreamInput | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> InvokeModelWithBidirectionalStreamInput:
        self._result = None
        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_INPUT, self._consumer
        )

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(
                    InvokeModelWithBidirectionalStreamInputChunk.deserialize(de)
                )

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: InvokeModelWithBidirectionalStreamInput) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class InvokeModelWithBidirectionalStreamOperationInput:
    """

    :param model_id:
        **[Required]** - The model ID or ARN of the model ID to use. Currently, only
        ``amazon.nova-sonic-v1:0`` is supported.

    """

    model_id: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT.members[
                            "modelId"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT,
            consumer=_consumer,
        )
        return kwargs


@dataclass(kw_only=True)
class BidirectionalOutputPayloadPart:
    """
    Output from the bidirectional stream. The output is speech and a text
    transcription.

    :param bytes_:
        The speech output of the bidirectional stream.

    """

    bytes_: bytes | None = field(repr=False, default=None)

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_BIDIRECTIONAL_OUTPUT_PAYLOAD_PART, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.bytes_ is not None:
            serializer.write_blob(
                _SCHEMA_BIDIRECTIONAL_OUTPUT_PAYLOAD_PART.members["bytes"], self.bytes_
            )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["bytes_"] = de.read_blob(
                        _SCHEMA_BIDIRECTIONAL_OUTPUT_PAYLOAD_PART.members["bytes"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_BIDIRECTIONAL_OUTPUT_PAYLOAD_PART, consumer=_consumer
        )
        return kwargs


@dataclass
class InvokeModelWithBidirectionalStreamOutputChunk:
    """
    The speech chunk that was provided as output from the invocation step.

    """

    value: BidirectionalOutputPayloadPart

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members["chunk"],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=BidirectionalOutputPayloadPart.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputInternalServerException:
    """
    The request encountered an unknown internal error.

    """

    value: InternalServerException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "internalServerException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=InternalServerException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputModelStreamErrorException:
    """
    The request encountered an error with the model stream.

    """

    value: ModelStreamErrorException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "modelStreamErrorException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ModelStreamErrorException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputValidationException:
    """
    The input fails to satisfy the constraints specified by an Amazon Web Services
    service.

    """

    value: ValidationException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "validationException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ValidationException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputThrottlingException:
    """
    The request was denied due to request throttling.

    """

    value: ThrottlingException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "throttlingException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ThrottlingException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputModelTimeoutException:
    """
    The connection was closed because a request was not received within the timeout
    period.

    """

    value: ModelTimeoutException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "modelTimeoutException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ModelTimeoutException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputServiceUnavailableException:
    """
    The request has failed due to a temporary failure of the server.

    """

    value: ServiceUnavailableException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT.members[
                "serviceUnavailableException"
            ],
            self.value,
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ServiceUnavailableException.deserialize(deserializer))


@dataclass
class InvokeModelWithBidirectionalStreamOutputUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


InvokeModelWithBidirectionalStreamOutput = Union[
    InvokeModelWithBidirectionalStreamOutputChunk
    | InvokeModelWithBidirectionalStreamOutputInternalServerException
    | InvokeModelWithBidirectionalStreamOutputModelStreamErrorException
    | InvokeModelWithBidirectionalStreamOutputValidationException
    | InvokeModelWithBidirectionalStreamOutputThrottlingException
    | InvokeModelWithBidirectionalStreamOutputModelTimeoutException
    | InvokeModelWithBidirectionalStreamOutputServiceUnavailableException
    | InvokeModelWithBidirectionalStreamOutputUnknown
]

"""
Output from the bidirectional stream that was used for model invocation.

"""


class _InvokeModelWithBidirectionalStreamOutputDeserializer:
    _result: InvokeModelWithBidirectionalStreamOutput | None = None

    def deserialize(
        self, deserializer: ShapeDeserializer
    ) -> InvokeModelWithBidirectionalStreamOutput:
        self._result = None
        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OUTPUT, self._consumer
        )

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputChunk.deserialize(de)
                )

            case 1:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputInternalServerException.deserialize(
                        de
                    )
                )

            case 2:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputModelStreamErrorException.deserialize(
                        de
                    )
                )

            case 3:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputValidationException.deserialize(
                        de
                    )
                )

            case 4:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputThrottlingException.deserialize(
                        de
                    )
                )

            case 5:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputModelTimeoutException.deserialize(
                        de
                    )
                )

            case 6:
                self._set_result(
                    InvokeModelWithBidirectionalStreamOutputServiceUnavailableException.deserialize(
                        de
                    )
                )

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: InvokeModelWithBidirectionalStreamOutput) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class InvokeModelWithBidirectionalStreamOperationOutput:
    """ """

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_OUTPUT, self
        )

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_OUTPUT,
            consumer=_consumer,
        )
        return kwargs


INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM = APIOperation(
    input=InvokeModelWithBidirectionalStreamOperationInput,
    output=InvokeModelWithBidirectionalStreamOperationOutput,
    schema=_SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM,
    input_schema=_SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_INPUT,
    output_schema=_SCHEMA_INVOKE_MODEL_WITH_BIDIRECTIONAL_STREAM_OPERATION_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelErrorException"
            ): ModelErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelNotReadyException"
            ): ModelNotReadyException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelStreamErrorException"
            ): ModelStreamErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelTimeoutException"
            ): ModelTimeoutException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceQuotaExceededException"
            ): ServiceQuotaExceededException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)


@dataclass(kw_only=True)
class InvokeModelWithResponseStreamInput:
    """

    :param body:
        The prompt and inference parameters in the format specified in the
        ``contentType`` in the header. You must provide the body in JSON format. To see the format and content of the request and response bodies for different models, refer to `Inference parameters <https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html>`_.
        For more information, see `Run inference <https://docs.aws.amazon.com/bedrock/latest/userguide/api-methods-run.html>`_
        in the Bedrock User Guide.

    :param content_type:
        The MIME type of the input data in the request. You must specify
        ``application/json``.

    :param accept:
        The desired MIME type of the inference body in the response. The default value
        is ``application/json``.

    :param model_id:
        **[Required]** - The unique identifier of the model to invoke to run inference.

        The ``modelId`` to provide depends on the type of model or throughput that you
        use:

        * If you use a base model, specify the model ID or its ARN. For a list of model
          IDs for base models, see `Amazon Bedrock base model IDs (on-demand throughput) <https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns>`_
          in the Amazon Bedrock User Guide.

        * If you use an inference profile, specify the inference profile ID or its ARN.
          For a list of inference profile IDs, see `Supported Regions and models for cross-region inference <https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a provisioned model, specify the ARN of the Provisioned Throughput.
          For more information, see `Run inference using a Provisioned Throughput <https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use a custom model, first purchase Provisioned Throughput for it. Then
          specify the ARN of the resulting provisioned model. For more information, see
          `Use a custom model in Amazon Bedrock <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html>`_
          in the Amazon Bedrock User Guide.

        * If you use an `imported model <https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html>`_,
          specify the ARN of the imported model. You can get the model ARN from a
          successful call to `CreateModelImportJob <https://docs.aws.amazon.com/bedrock/latest/APIReference/API_CreateModelImportJob.html>`_
          or from the Imported models page in the Amazon Bedrock console.

    :param trace:
        Specifies whether to enable or disable the Bedrock trace. If enabled, you can
        see the full Bedrock trace.

    :param guardrail_identifier:
        The unique identifier of the guardrail that you want to use. If you don't
        provide a value, no guardrail is applied to the invocation.

        An error is thrown in the following situations.

        * You don't provide a guardrail identifier but you specify the
          ``amazon-bedrock-guardrailConfig`` field in the request body.

        * You enable the guardrail but the ``contentType`` isn't ``application/json``.

        * You provide a guardrail identifier, but ``guardrailVersion`` isn't specified.

    :param guardrail_version:
        The version number for the guardrail. The value can also be ``DRAFT``.

    :param performance_config_latency:
        Model performance settings for the request.

    """

    body: bytes | None = field(repr=False, default=None)
    content_type: str | None = None
    accept: str | None = None
    model_id: str | None = None
    trace: str | None = None
    guardrail_identifier: str | None = None
    guardrail_version: str | None = None
    performance_config_latency: str = "standard"

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["body"] = de.read_blob(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members["body"]
                    )

                case 1:
                    kwargs["content_type"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "contentType"
                        ]
                    )

                case 2:
                    kwargs["accept"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "accept"
                        ]
                    )

                case 3:
                    kwargs["model_id"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "modelId"
                        ]
                    )

                case 4:
                    kwargs["trace"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members["trace"]
                    )

                case 5:
                    kwargs["guardrail_identifier"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "guardrailIdentifier"
                        ]
                    )

                case 6:
                    kwargs["guardrail_version"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "guardrailVersion"
                        ]
                    )

                case 7:
                    kwargs["performance_config_latency"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT.members[
                            "performanceConfigLatency"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT, consumer=_consumer
        )
        return kwargs


@dataclass(kw_only=True)
class PayloadPart:
    """
    Payload content included in the response.

    :param bytes_:
        Base64-encoded bytes of payload data.

    """

    bytes_: bytes | None = field(repr=False, default=None)

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_PAYLOAD_PART, self)

    def serialize_members(self, serializer: ShapeSerializer):
        if self.bytes_ is not None:
            serializer.write_blob(_SCHEMA_PAYLOAD_PART.members["bytes"], self.bytes_)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["bytes_"] = de.read_blob(
                        _SCHEMA_PAYLOAD_PART.members["bytes"]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(_SCHEMA_PAYLOAD_PART, consumer=_consumer)
        return kwargs


@dataclass
class ResponseStreamChunk:
    """
    Content included in the response.

    """

    value: PayloadPart

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM.members["chunk"], self.value)

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=PayloadPart.deserialize(deserializer))


@dataclass
class ResponseStreamInternalServerException:
    """
    An internal server error occurred. Retry your request.

    """

    value: InternalServerException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["internalServerException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=InternalServerException.deserialize(deserializer))


@dataclass
class ResponseStreamModelStreamErrorException:
    """
    An error occurred while streaming the response. Retry your request.

    """

    value: ModelStreamErrorException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["modelStreamErrorException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ModelStreamErrorException.deserialize(deserializer))


@dataclass
class ResponseStreamValidationException:
    """
    Input validation failed. Check your request parameters and retry the request.

    """

    value: ValidationException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["validationException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ValidationException.deserialize(deserializer))


@dataclass
class ResponseStreamThrottlingException:
    """
    Your request was throttled because of service-wide limitations. Resubmit your
    request later or in a different region. You can also purchase `Provisioned Throughput <https://docs.aws.amazon.com/bedrock/latest/userguide/prov-throughput.html>`_
    to increase the rate or number of tokens you can process.

    """

    value: ThrottlingException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["throttlingException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ThrottlingException.deserialize(deserializer))


@dataclass
class ResponseStreamModelTimeoutException:
    """
    The request took too long to process. Processing time exceeded the model timeout
    length.

    """

    value: ModelTimeoutException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["modelTimeoutException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ModelTimeoutException.deserialize(deserializer))


@dataclass
class ResponseStreamServiceUnavailableException:
    """
    The service isn't available. Try again later.

    """

    value: ServiceUnavailableException

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_RESPONSE_STREAM, self)

    def serialize_members(self, serializer: ShapeSerializer):
        serializer.write_struct(
            _SCHEMA_RESPONSE_STREAM.members["serviceUnavailableException"], self.value
        )

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(value=ServiceUnavailableException.deserialize(deserializer))


@dataclass
class ResponseStreamUnknown:
    """Represents an unknown variant.

    If you receive this value, you will need to update your library to receive the
    parsed value.

    This value may not be deliberately sent.
    """

    tag: str

    def serialize(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    def serialize_members(self, serializer: ShapeSerializer):
        raise SmithyException("Unknown union variants may not be serialized.")

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        raise NotImplementedError()


ResponseStream = Union[
    ResponseStreamChunk
    | ResponseStreamInternalServerException
    | ResponseStreamModelStreamErrorException
    | ResponseStreamValidationException
    | ResponseStreamThrottlingException
    | ResponseStreamModelTimeoutException
    | ResponseStreamServiceUnavailableException
    | ResponseStreamUnknown
]

"""
Definition of content in the response stream.

"""


class _ResponseStreamDeserializer:
    _result: ResponseStream | None = None

    def deserialize(self, deserializer: ShapeDeserializer) -> ResponseStream:
        self._result = None
        deserializer.read_struct(_SCHEMA_RESPONSE_STREAM, self._consumer)

        if self._result is None:
            raise SmithyException("Unions must have exactly one value, but found none.")

        return self._result

    def _consumer(self, schema: Schema, de: ShapeDeserializer) -> None:
        match schema.expect_member_index():
            case 0:
                self._set_result(ResponseStreamChunk.deserialize(de))

            case 1:
                self._set_result(ResponseStreamInternalServerException.deserialize(de))

            case 2:
                self._set_result(
                    ResponseStreamModelStreamErrorException.deserialize(de)
                )

            case 3:
                self._set_result(ResponseStreamValidationException.deserialize(de))

            case 4:
                self._set_result(ResponseStreamThrottlingException.deserialize(de))

            case 5:
                self._set_result(ResponseStreamModelTimeoutException.deserialize(de))

            case 6:
                self._set_result(
                    ResponseStreamServiceUnavailableException.deserialize(de)
                )

            case _:
                logger.debug("Unexpected member schema: %s", schema)

    def _set_result(self, value: ResponseStream) -> None:
        if self._result is not None:
            raise SmithyException(
                "Unions must have exactly one value, but found more than one."
            )
        self._result = value


@dataclass(kw_only=True)
class InvokeModelWithResponseStreamOutput:
    """

    :param content_type:
        **[Required]** - The MIME type of the inference result.

    :param performance_config_latency:
        Model performance settings for the request.

    """

    content_type: str

    performance_config_latency: str | None = None

    def serialize(self, serializer: ShapeSerializer):
        serializer.write_struct(_SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT, self)

    def serialize_members(self, serializer: ShapeSerializer):
        pass

    @classmethod
    def deserialize(cls, deserializer: ShapeDeserializer) -> Self:
        return cls(**cls.deserialize_kwargs(deserializer))

    @classmethod
    def deserialize_kwargs(cls, deserializer: ShapeDeserializer) -> dict[str, Any]:
        kwargs: dict[str, Any] = {}

        def _consumer(schema: Schema, de: ShapeDeserializer) -> None:
            match schema.expect_member_index():
                case 0:
                    kwargs["content_type"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT.members[
                            "contentType"
                        ]
                    )

                case 1:
                    kwargs["performance_config_latency"] = de.read_string(
                        _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT.members[
                            "performanceConfigLatency"
                        ]
                    )

                case _:
                    logger.debug("Unexpected member schema: %s", schema)

        deserializer.read_struct(
            _SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT, consumer=_consumer
        )
        return kwargs


INVOKE_MODEL_WITH_RESPONSE_STREAM = APIOperation(
    input=InvokeModelWithResponseStreamInput,
    output=InvokeModelWithResponseStreamOutput,
    schema=_SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM,
    input_schema=_SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_INPUT,
    output_schema=_SCHEMA_INVOKE_MODEL_WITH_RESPONSE_STREAM_OUTPUT,
    error_registry=TypeRegistry(
        {
            ShapeID(
                "com.amazonaws.bedrockruntime#AccessDeniedException"
            ): AccessDeniedException,
            ShapeID(
                "com.amazonaws.bedrockruntime#InternalServerException"
            ): InternalServerException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelErrorException"
            ): ModelErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelNotReadyException"
            ): ModelNotReadyException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelStreamErrorException"
            ): ModelStreamErrorException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ModelTimeoutException"
            ): ModelTimeoutException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ResourceNotFoundException"
            ): ResourceNotFoundException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceQuotaExceededException"
            ): ServiceQuotaExceededException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ServiceUnavailableException"
            ): ServiceUnavailableException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ThrottlingException"
            ): ThrottlingException,
            ShapeID(
                "com.amazonaws.bedrockruntime#ValidationException"
            ): ValidationException,
        }
    ),
    effective_auth_schemes=[ShapeID("aws.auth#sigv4")],
)
