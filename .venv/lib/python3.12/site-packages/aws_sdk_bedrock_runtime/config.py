# Code generated by smithy-python-codegen DO NOT EDIT.

from dataclasses import dataclass
from typing import Any, Callable, TypeAlias, Union

from smithy_aws_core.auth import SigV4AuthScheme
from smithy_aws_core.endpoints.standard_regional import (
    StandardRegionalEndpointsResolver as _RegionalResolver,
)
from smithy_aws_core.identity import AWSCredentialsIdentity
from smithy_core.aio.interfaces import EndpointResolver as _EndpointResolver
from smithy_core.aio.interfaces.identity import IdentityResolver
from smithy_core.interceptors import Interceptor
from smithy_core.interfaces import URI
from smithy_core.interfaces.identity import IdentityProperties
from smithy_core.interfaces.retries import RetryStrategy
from smithy_core.retries import SimpleRetryStrategy
from smithy_http.aio.crt import AWSCRTHTTPClient
from smithy_http.aio.interfaces import HTTPClient
from smithy_http.aio.interfaces.auth import HTTPAuthScheme
from smithy_http.interfaces import HTTPRequestConfiguration

from .auth import HTTPAuthSchemeResolver
from .models import (
    ApplyGuardrailInput,
    ApplyGuardrailOutput,
    ConverseInput,
    ConverseOperationOutput,
    ConverseStreamInput,
    ConverseStreamOperationOutput,
    GetAsyncInvokeInput,
    GetAsyncInvokeOutput,
    InvokeModelInput,
    InvokeModelOutput,
    InvokeModelWithBidirectionalStreamOperationInput,
    InvokeModelWithBidirectionalStreamOperationOutput,
    InvokeModelWithResponseStreamInput,
    InvokeModelWithResponseStreamOutput,
    ListAsyncInvokesInput,
    ListAsyncInvokesOutput,
    StartAsyncInvokeInput,
    StartAsyncInvokeOutput,
)


_ServiceInterceptor = Union[
    Interceptor[ApplyGuardrailInput, ApplyGuardrailOutput, Any, Any],
    Interceptor[ConverseInput, ConverseOperationOutput, Any, Any],
    Interceptor[ConverseStreamInput, ConverseStreamOperationOutput, Any, Any],
    Interceptor[GetAsyncInvokeInput, GetAsyncInvokeOutput, Any, Any],
    Interceptor[InvokeModelInput, InvokeModelOutput, Any, Any],
    Interceptor[
        InvokeModelWithBidirectionalStreamOperationInput,
        InvokeModelWithBidirectionalStreamOperationOutput,
        Any,
        Any,
    ],
    Interceptor[
        InvokeModelWithResponseStreamInput,
        InvokeModelWithResponseStreamOutput,
        Any,
        Any,
    ],
    Interceptor[ListAsyncInvokesInput, ListAsyncInvokesOutput, Any, Any],
    Interceptor[StartAsyncInvokeInput, StartAsyncInvokeOutput, Any, Any],
]


@dataclass(init=False)
class Config:
    """Configuration for AmazonBedrockFrontendService."""

    aws_credentials_identity_resolver: (
        IdentityResolver[AWSCredentialsIdentity, IdentityProperties] | None
    )
    endpoint_resolver: _EndpointResolver
    endpoint_uri: str | URI | None
    http_auth_scheme_resolver: HTTPAuthSchemeResolver
    http_auth_schemes: dict[str, HTTPAuthScheme[Any, Any, Any, Any]]
    http_client: HTTPClient
    http_request_config: HTTPRequestConfiguration | None
    interceptors: list[_ServiceInterceptor]
    region: str | None
    retry_strategy: RetryStrategy
    sdk_ua_app_id: str | None
    user_agent_extra: str | None

    def __init__(
        self,
        *,
        aws_credentials_identity_resolver: IdentityResolver[
            AWSCredentialsIdentity, IdentityProperties
        ]
        | None = None,
        endpoint_resolver: _EndpointResolver | None = None,
        endpoint_uri: str | URI | None = None,
        http_auth_scheme_resolver: HTTPAuthSchemeResolver | None = None,
        http_auth_schemes: dict[str, HTTPAuthScheme[Any, Any, Any, Any]] | None = None,
        http_client: HTTPClient | None = None,
        http_request_config: HTTPRequestConfiguration | None = None,
        interceptors: list[_ServiceInterceptor] | None = None,
        region: str | None = None,
        retry_strategy: RetryStrategy | None = None,
        sdk_ua_app_id: str | None = None,
        user_agent_extra: str | None = None,
    ):
        """Constructor.

        :param aws_credentials_identity_resolver:
             Resolves AWS Credentials. Required for operations that use Sigv4 Auth.

        :param endpoint_resolver:
             The endpoint resolver used to resolve the final endpoint per-operation based on
             the configuration.

        :param endpoint_uri:
             A static URI to route requests to.

        :param http_auth_scheme_resolver:
             An http auth scheme resolver that determines the auth scheme for each operation.

        :param http_auth_schemes:
             A map of http auth scheme ids to http auth schemes.

        :param http_client:
             The HTTP client used to make requests.

        :param http_request_config:
             Configuration for individual HTTP requests.

        :param interceptors:
             The list of interceptors, which are hooks that are called during the execution
             of a request.

        :param region:
             The AWS region to connect to. The configured region is used to determine the
             service endpoint.

        :param retry_strategy:
             The retry strategy for issuing retry tokens and computing retry delays.

        :param sdk_ua_app_id:
             A unique and opaque application ID that is appended to the User-Agent header.

        :param user_agent_extra:
             Additional suffix to be added to the User-Agent header.

        """
        self.aws_credentials_identity_resolver = aws_credentials_identity_resolver
        self.endpoint_resolver = endpoint_resolver or _RegionalResolver(
            endpoint_prefix="bedrock-runtime"
        )
        self.endpoint_uri = endpoint_uri
        self.http_auth_scheme_resolver = (
            http_auth_scheme_resolver or HTTPAuthSchemeResolver()
        )
        self.http_auth_schemes = http_auth_schemes or {
            "aws.auth#sigv4": SigV4AuthScheme(),
        }

        self.http_client = http_client or AWSCRTHTTPClient()
        self.http_request_config = http_request_config
        self.interceptors = interceptors or []
        self.region = region
        self.retry_strategy = retry_strategy or SimpleRetryStrategy()
        self.sdk_ua_app_id = sdk_ua_app_id
        self.user_agent_extra = user_agent_extra

    def set_http_auth_scheme(self, scheme: HTTPAuthScheme[Any, Any, Any, Any]) -> None:
        """Sets the implementation of an auth scheme.

        Using this method ensures the correct key is used.

        :param scheme: The auth scheme to add.
        """
        self.http_auth_schemes[scheme.scheme_id] = scheme


#
# A callable that allows customizing the config object on each request.
#
Plugin: TypeAlias = Callable[[Config], None]
