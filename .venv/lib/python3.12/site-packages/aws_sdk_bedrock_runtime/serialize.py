# Code generated by smithy-python-codegen DO NOT EDIT.

from typing import AsyncIterable
from urllib.parse import quote as urlquote

from smithy_core import URI as _URI
from smithy_core.aio.types import (
    AsyncBytesProvider,
    AsyncBytesReader,
    SeekableAsyncBytesReader,
)
from smithy_core.types import TimestampFormat
from smithy_core.utils import ensure_utc, serialize_rfc3339
from smithy_http import Field, Fields
from smithy_http.aio import HTTPRequest as _HTTPRequest
from smithy_http.aio.interfaces import HTTPRequest
from smithy_http.utils import join_query_params
from smithy_json import JSONCodec

from .config import Config
from .models import (
    ApplyGuardrailInput,
    ConverseInput,
    ConverseStreamInput,
    GetAsyncInvokeInput,
    InvokeModelInput,
    InvokeModelWithBidirectionalStreamOperationInput,
    InvokeModelWithResponseStreamInput,
    ListAsyncInvokesInput,
    ServiceError,
    StartAsyncInvokeInput,
)


async def _serialize_apply_guardrail(
    input: ApplyGuardrailInput, config: Config
) -> HTTPRequest:
    if not input.guardrail_identifier:
        raise ServiceError("guardrail_identifier must not be empty.")

    if not input.guardrail_version:
        raise ServiceError("guardrail_version must not be empty.")

    path = "/guardrail/{guardrail_identifier}/version/{guardrail_version}/apply".format(
        guardrail_identifier=urlquote(input.guardrail_identifier, safe=""),
        guardrail_version=urlquote(input.guardrail_version, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
    content = codec.serialize(input)
    if not content:
        content = b"{}"
    content_length = len(content)
    body = SeekableAsyncBytesReader(content)

    headers = Fields(
        [
            Field(name="Content-Type", values=["application/json"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_converse(input: ConverseInput, config: Config) -> HTTPRequest:
    if not input.model_id:
        raise ServiceError("model_id must not be empty.")

    path = "/model/{model_id}/converse".format(
        model_id=urlquote(input.model_id, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
    content = codec.serialize(input)
    if not content:
        content = b"{}"
    content_length = len(content)
    body = SeekableAsyncBytesReader(content)

    headers = Fields(
        [
            Field(name="Content-Type", values=["application/json"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_converse_stream(
    input: ConverseStreamInput, config: Config
) -> HTTPRequest:
    if not input.model_id:
        raise ServiceError("model_id must not be empty.")

    path = "/model/{model_id}/converse-stream".format(
        model_id=urlquote(input.model_id, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
    content = codec.serialize(input)
    if not content:
        content = b"{}"
    content_length = len(content)
    body = SeekableAsyncBytesReader(content)

    headers = Fields(
        [
            Field(name="Content-Type", values=["application/json"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_get_async_invoke(
    input: GetAsyncInvokeInput, config: Config
) -> HTTPRequest:
    if not input.invocation_arn:
        raise ServiceError("invocation_arn must not be empty.")

    path = "/async-invoke/{invocation_arn}".format(
        invocation_arn=urlquote(input.invocation_arn, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    headers = Fields([])

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="GET",
        fields=headers,
        body=body,
    )


async def _serialize_invoke_model(
    input: InvokeModelInput, config: Config
) -> HTTPRequest:
    if not input.model_id:
        raise ServiceError("model_id must not be empty.")

    path = "/model/{model_id}/invoke".format(
        model_id=urlquote(input.model_id, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    content_length: int = 0
    if input.body is not None:
        content_length = len(input.body)
        body = SeekableAsyncBytesReader(input.body)
    headers = Fields(
        [
            Field(name="Content-Type", values=["application/octet-stream"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    if input.content_type:
        headers.extend(
            Fields([Field(name="Content-Type", values=[input.content_type])])
        )
    if input.accept:
        headers.extend(Fields([Field(name="Accept", values=[input.accept])]))
    if input.trace:
        headers.extend(
            Fields([Field(name="X-Amzn-Bedrock-Trace", values=[input.trace])])
        )
    if input.guardrail_identifier:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-GuardrailIdentifier",
                        values=[input.guardrail_identifier],
                    )
                ]
            )
        )
    if input.guardrail_version:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-GuardrailVersion",
                        values=[input.guardrail_version],
                    )
                ]
            )
        )
    if input.performance_config_latency:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-PerformanceConfig-Latency",
                        values=[input.performance_config_latency],
                    )
                ]
            )
        )
    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_invoke_model_with_bidirectional_stream(
    input: InvokeModelWithBidirectionalStreamOperationInput, config: Config
) -> HTTPRequest:
    if not input.model_id:
        raise ServiceError("model_id must not be empty.")

    path = "/model/{model_id}/invoke-with-bidirectional-stream".format(
        model_id=urlquote(input.model_id, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    body = AsyncBytesProvider()
    headers = Fields(
        [
            Field(name="Content-Type", values=["application/vnd.amazon.eventstream"]),
            Field(
                name="X-Amz-Content-SHA256",
                values=["STREAMING-AWS4-HMAC-SHA256-EVENTS"],
            ),
        ]
    )

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_invoke_model_with_response_stream(
    input: InvokeModelWithResponseStreamInput, config: Config
) -> HTTPRequest:
    if not input.model_id:
        raise ServiceError("model_id must not be empty.")

    path = "/model/{model_id}/invoke-with-response-stream".format(
        model_id=urlquote(input.model_id, safe=""),
    )
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    content_length: int = 0
    if input.body is not None:
        content_length = len(input.body)
        body = SeekableAsyncBytesReader(input.body)
    headers = Fields(
        [
            Field(name="Content-Type", values=["application/octet-stream"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    if input.content_type:
        headers.extend(
            Fields([Field(name="Content-Type", values=[input.content_type])])
        )
    if input.accept:
        headers.extend(
            Fields([Field(name="X-Amzn-Bedrock-Accept", values=[input.accept])])
        )
    if input.trace:
        headers.extend(
            Fields([Field(name="X-Amzn-Bedrock-Trace", values=[input.trace])])
        )
    if input.guardrail_identifier:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-GuardrailIdentifier",
                        values=[input.guardrail_identifier],
                    )
                ]
            )
        )
    if input.guardrail_version:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-GuardrailVersion",
                        values=[input.guardrail_version],
                    )
                ]
            )
        )
    if input.performance_config_latency:
        headers.extend(
            Fields(
                [
                    Field(
                        name="X-Amzn-Bedrock-PerformanceConfig-Latency",
                        values=[input.performance_config_latency],
                    )
                ]
            )
        )
    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )


async def _serialize_list_async_invokes(
    input: ListAsyncInvokesInput, config: Config
) -> HTTPRequest:
    path = "/async-invoke"
    query: str = ""

    query_params: list[tuple[str, str | None]] = []
    if input.submit_time_after is not None:
        query_params.append(
            ("submitTimeAfter", serialize_rfc3339(ensure_utc(input.submit_time_after)))
        )
    if input.submit_time_before is not None:
        query_params.append(
            (
                "submitTimeBefore",
                serialize_rfc3339(ensure_utc(input.submit_time_before)),
            )
        )
    if input.status_equals is not None:
        query_params.append(("statusEquals", input.status_equals))
    if input.max_results is not None:
        query_params.append(("maxResults", str(input.max_results)))
    if input.next_token is not None:
        query_params.append(("nextToken", input.next_token))
    if input.sort_by is not None:
        query_params.append(("sortBy", input.sort_by))
    if input.sort_order is not None:
        query_params.append(("sortOrder", input.sort_order))

    query = join_query_params(params=query_params, prefix=query)

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    headers = Fields([])

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="GET",
        fields=headers,
        body=body,
    )


async def _serialize_start_async_invoke(
    input: StartAsyncInvokeInput, config: Config
) -> HTTPRequest:
    path = "/async-invoke"
    query: str = ""

    body: AsyncIterable[bytes] = AsyncBytesReader(b"")
    codec = JSONCodec(default_timestamp_format=TimestampFormat.EPOCH_SECONDS)
    content = codec.serialize(input)
    if not content:
        content = b"{}"
    content_length = len(content)
    body = SeekableAsyncBytesReader(content)

    headers = Fields(
        [
            Field(name="Content-Type", values=["application/json"]),
            Field(name="Content-Length", values=[str(content_length)]),
        ]
    )

    return _HTTPRequest(
        destination=_URI(
            host="",
            path=path,
            scheme="https",
            query=query,
        ),
        method="POST",
        fields=headers,
        body=body,
    )
