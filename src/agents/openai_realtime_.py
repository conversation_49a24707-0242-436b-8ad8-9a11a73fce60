import threading
import pyaudio
import queue
import base64
import json
import os
import signal
import time
import asyncio
import datetime
from websocket import create_connection, WebSocketConnectionClosedException
from dotenv import load_dotenv
import logging
import sys
import re
import numpy as np

# Add the project root to the path to import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.agents.realtime.knowledgebase import knowledge_retrieval
from src.agents.realtime.prompts import PromptTemplates, InterviewInstructions, QuestionGeneration
from src.agents.realtime.metrics import MetricsCollector

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

class SafeJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles numpy types and other non-serializable objects."""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif hasattr(obj, 'tolist'):  # Handle other numpy types
            return obj.tolist()
        elif isinstance(obj, bytes):
            return obj.decode('utf-8', errors='ignore')
        return super().default(obj)

load_dotenv()

CHUNK_SIZE = 1024
RATE = 24000
FORMAT = pyaudio.paInt16
REENGAGE_DELAY_MS = 500
# Audio buffer settings to prevent underruns
AUDIO_BUFFER_SIZE = 4096  # Larger buffer size
FRAMES_PER_BUFFER = 1024  # Smaller frames per buffer for better responsiveness


class Socket:
    def __init__(self, api_key, ws_url):
        self.api_key = api_key
        self.ws_url = ws_url
        self.ws = None
        self.on_msg = None
        self._stop_event = threading.Event()
        self.recv_thread = None
        self.lock = threading.Lock()
        self.realtime_instance = None

    def connect(self):
        self.ws = create_connection(self.ws_url, header=[f'Authorization: Bearer {self.api_key}', 'OpenAI-Beta: realtime=v1'])
        logging.info('Connected to WebSocket.')
        self.recv_thread = threading.Thread(target=self._receive_messages, daemon=True)
        self.recv_thread.start()

    def _receive_messages(self):
        while not self._stop_event.is_set():
            try:
                # Set a timeout for recv to make it non-blocking
                self.ws.settimeout(1.0)  # 1 second timeout
                message = self.ws.recv()
                if message and self.on_msg:
                    self.on_msg(json.loads(message))
            except WebSocketConnectionClosedException:
                logging.error('WebSocket connection closed.')
                # Mark WebSocket as disconnected
                if hasattr(self, 'realtime_instance') and self.realtime_instance:
                    self.realtime_instance.websocket_connected = False
                break
            except Exception as e:
                if "timed out" not in str(e).lower():
                    logging.error(f'Error receiving message: {e}')
                # Continue the loop for timeout exceptions
        logging.info('Exiting WebSocket receiving thread.')

    def send(self, data):
        try:
            with self.lock:
                if self.ws:
                    self.ws.send(json.dumps(data))
        except WebSocketConnectionClosedException:
            logging.error('WebSocket connection closed.')
        except Exception as e:
            logging.error(f'Error sending message: {e}')

    def kill(self):
        self._stop_event.set()
        if self.ws:
            try:
                self.ws.send_close()
                self.ws.close()
                logging.info('WebSocket connection closed.')
            except Exception as e:
                logging.error(f'Error closing WebSocket: {e}')
        if self.recv_thread:
            # Don't join the thread - let it die naturally as a daemon thread
            logging.info('WebSocket receive thread will terminate as daemon thread')
    
    @property
    def connected(self):
        """Check if WebSocket is connected"""
        return self.ws is not None and not self._stop_event.is_set()
    
    @property
    def closed(self):
        """Check if WebSocket is closed"""
        return self.ws is None or self._stop_event.is_set()


class AudioIO:
    def __init__(self, chunk_size=CHUNK_SIZE, rate=RATE, format=FORMAT):
        self.chunk_size = chunk_size
        self.rate = rate
        self.format = format
        self.audio_buffer = bytearray()
        self.mic_queue = queue.Queue()
        self.mic_on_at = 0
        self.mic_active = None
        self._stop_event = threading.Event()
        self.p = pyaudio.PyAudio()
        
        # Continuous recording system - unified at 24kHz
        self.continuous_audio = []  # All audio in chronological order
        self.audio_timestamps = []  # Timestamps for each audio chunk
        self.is_recording = False
        self.recording_started = False
        self.recording_lock = threading.Lock()  # Thread safety for continuous recording
        self.session_start_time = time.time()  # For relative timing

    def _resample_audio(self, audio_data, from_rate, to_rate):
        """Resample audio data from one sample rate to another"""
        try:
            import numpy as np
            import librosa
            
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Resample using librosa
            resampled = librosa.resample(
                audio_array.astype(np.float32), 
                orig_sr=from_rate, 
                target_sr=to_rate
            )
            
            # Convert back to int16 and then to bytes
            resampled_int16 = resampled.astype(np.int16)
            return resampled_int16.tobytes()
            
        except Exception as e:
            logging.warning(f"Resampling failed: {e}, using original audio")
            return audio_data

    def _mic_callback(self, in_data, frame_count, time_info, status):
        if time.time() > self.mic_on_at:
            if not self.mic_active:
                logging.info('🎙️🟢 Mic active')
                self.mic_active = True
            self.mic_queue.put(in_data)
            
            # Record user audio continuously if recording is enabled
            if self.is_recording:
                with self.recording_lock:
                    # Convert 16kHz user audio to 24kHz for consistency
                    user_audio_24k = self._resample_audio(in_data, 16000, 24000)
                    self.continuous_audio.append({
                        'type': 'user',
                        'data': user_audio_24k,
                        'timestamp': time.time() - self.session_start_time,  # Relative timestamp
                        'sample_rate': 24000  # Unified sample rate
                    })
                    self.audio_timestamps.append(time.time())
                    if len(self.continuous_audio) % 100 == 0:  # Log every 100 chunks
                        logging.info(f"🎵 Recording: {len(self.continuous_audio)} audio chunks captured")
                
            # Calculate audio level for barge-in detection
            import struct
            audio_level = 0
            if len(in_data) > 0:
                # Convert bytes to samples and calculate RMS
                samples = struct.unpack(f'<{len(in_data)//2}h', in_data)
                audio_level = (sum(s*s for s in samples) / len(samples)) ** 0.5
                
        else:
            if self.mic_active:
                logging.info('🎙️🔴 Mic suppressed')
                self.mic_active = False
        return (None, pyaudio.paContinue)

    def _spkr_callback(self, in_data, frame_count, time_info, status):
        bytes_needed = frame_count * 2
        current_buffer_size = len(self.audio_buffer)

        if current_buffer_size >= bytes_needed:
            audio_chunk = bytes(self.audio_buffer[:bytes_needed])
            self.audio_buffer = self.audio_buffer[bytes_needed:]
            self.mic_on_at = time.time() + REENGAGE_DELAY_MS / 1000
            # Log every 20th audio chunk to reduce noise
            if not hasattr(self, '_speaker_play_count'):
                self._speaker_play_count = 0
            self._speaker_play_count += 1
            if self._speaker_play_count % 20 == 0:
                logging.info(f'🔊 Playing audio chunk {self._speaker_play_count} ({len(audio_chunk)} bytes)')
        else:
            # Improved buffer underrun handling
            if current_buffer_size > 0:
                # Use available audio + silence for the rest
                audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
                self.audio_buffer = bytearray()  # Clear the buffer after using it
            else:
                # No audio available, send silence
                audio_chunk = b'\x00' * bytes_needed
            
            # Only log buffer underruns occasionally to reduce noise
            if not hasattr(self, '_last_underrun_log') or time.time() - self._last_underrun_log > 5:
                logging.warning(f'🔊 Buffer underrun: needed {bytes_needed}, had {current_buffer_size} - playing silence')
                self._last_underrun_log = time.time()

        return (audio_chunk, pyaudio.paContinue)

    def start_streams(self):
        # Use optimized buffer settings to prevent underruns
        self.mic_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            input=True,
            stream_callback=self._mic_callback,
            frames_per_buffer=FRAMES_PER_BUFFER,
            input_device_index=None,  # Use default device
            start=False  # Don't start immediately
        )
        self.spkr_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            output=True,
            stream_callback=self._spkr_callback,
            frames_per_buffer=FRAMES_PER_BUFFER,
            output_device_index=None,  # Use default device
            start=False  # Don't start immediately
        )
        
        # Start streams with proper error handling
        try:
            self.mic_stream.start_stream()
            self.spkr_stream.start_stream()
            logging.info("🎵 Audio streams started successfully")
        except Exception as e:
            logging.error(f"❌ Error starting audio streams: {e}")
            # Try with different buffer settings
            try:
                self.mic_stream = self.p.open(
                    format=self.format,
                    channels=1,
                    rate=self.rate,
                    input=True,
                    stream_callback=self._mic_callback,
                    frames_per_buffer=2048,  # Larger buffer as fallback
                    start=True
                )
                self.spkr_stream = self.p.open(
                    format=self.format,
                    channels=1,
                    rate=self.rate,
                    output=True,
                    stream_callback=self._spkr_callback,
                    frames_per_buffer=2048,  # Larger buffer as fallback
                    start=True
                )
                logging.info("🎵 Audio streams started with fallback settings")
            except Exception as e2:
                logging.error(f"❌ Failed to start audio streams even with fallback: {e2}")
                raise

    def start_recording(self):
        """Start recording audio"""
        if not self.is_recording:  # Only start if not already recording
            self.is_recording = True
            with self.recording_lock:
                self.continuous_audio = []
                self.audio_timestamps = []
            logging.info("🎵 Continuous recording started")
        
    def record_assistant_audio(self, audio_data):
        """Record assistant audio data in continuous stream"""
        if self.is_recording and audio_data:
            with self.recording_lock:
                self.continuous_audio.append({
                    'type': 'assistant',
                    'data': audio_data,
                    'timestamp': time.time() - self.session_start_time,  # Relative timestamp
                    'sample_rate': 24000  # Assistant audio is 24kHz
                })
                self.audio_timestamps.append(time.time())

    def stop_recording(self):
        """Stop recording audio"""
        self.is_recording = False
        with self.recording_lock:
            total_chunks = len(self.continuous_audio)
            user_chunks = len([a for a in self.continuous_audio if a['type'] == 'user'])
            assistant_chunks = len([a for a in self.continuous_audio if a['type'] == 'assistant'])
        logging.info("🎵 Continuous recording stopped")
        logging.info(f"🎵 Total audio chunks recorded: {total_chunks}")
        logging.info(f"🎵 User audio chunks: {user_chunks}")
        logging.info(f"🎵 Assistant audio chunks: {assistant_chunks}")

    def save_recording(self, filepath):
        """Save continuous recorded audio to file in chronological order"""
        logging.info(f"🎵 Attempting to save continuous audio recording...")
        
        with self.recording_lock:
            if not self.continuous_audio:
                logging.warning("No continuous audio recorded to save")
                return False
            
            total_chunks = len(self.continuous_audio)
            user_chunks = len([a for a in self.continuous_audio if a['type'] == 'user'])
            assistant_chunks = len([a for a in self.continuous_audio if a['type'] == 'assistant'])
            
            logging.info(f"🎵 Total audio chunks: {total_chunks}")
            logging.info(f"🎵 User chunks: {user_chunks}")
            logging.info(f"🎵 Assistant chunks: {assistant_chunks}")
            
        try:
            import wave
            import os
            import numpy as np
            import struct
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # Sort audio chunks by timestamp to ensure chronological order
            with self.recording_lock:
                sorted_audio = sorted(self.continuous_audio, key=lambda x: x['timestamp'])
            
            # Process audio chunks in chronological order (all at 24kHz now)
            processed_audio = []
            target_sample_rate = 24000  # All audio is now at 24kHz
            
            for i, chunk in enumerate(sorted_audio):
                audio_data = chunk['data']
                chunk_type = chunk['type']
                timestamp = chunk['timestamp']
                
                processed_audio.append(audio_data)
                
                # Log progress every 50 chunks
                if i % 50 == 0:
                    logging.info(f"🎵 Processed {i+1}/{len(sorted_audio)} chunks, current: {chunk_type} at {timestamp:.2f}s")
            
            # Save the continuous audio in chronological order
            with wave.open(filepath, 'wb') as wf:
                wf.setnchannels(1)  # Mono
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(target_sample_rate)  # 24kHz
                wf.writeframes(b''.join(processed_audio))
            
            file_size = os.path.getsize(filepath)
            total_duration = len(processed_audio) * (self.chunk_size / 2) / target_sample_rate  # Approximate duration
            
            logging.info(f"🎵 Continuous audio saved to {filepath}")
            logging.info(f"🎵 File size: {file_size} bytes")
            logging.info(f"🎵 Audio timeline: {len(processed_audio)} chunks in chronological order")
            logging.info(f"🎵 Estimated duration: {total_duration:.2f} seconds")
            logging.info(f"🎵 Sample rate: {target_sample_rate} Hz")
            return True
            
        except Exception as e:
            logging.error(f"Error saving continuous audio: {e}")
            return False

    def stop_streams(self):
        try:
            if hasattr(self, 'mic_stream') and self.mic_stream:
                self.mic_stream.stop_stream()
                self.mic_stream.close()
        except Exception as e:
            logging.warning(f"Error stopping mic stream: {e}")
        
        try:
            if hasattr(self, 'spkr_stream') and self.spkr_stream:
                self.spkr_stream.stop_stream()
                self.spkr_stream.close()
        except Exception as e:
            logging.warning(f"Error stopping speaker stream: {e}")
        
        try:
            if hasattr(self, 'p') and self.p:
                self.p.terminate()
        except Exception as e:
            logging.warning(f"Error terminating PyAudio: {e}")

    def send_mic_audio(self, socket, realtime_instance=None):
        while not self._stop_event.is_set():
            if not self.mic_queue.empty():
                mic_chunk = self.mic_queue.get()
                
                # Check for barge-in (user speaking while AI is speaking)
                if realtime_instance and realtime_instance.is_speaking:
                    # Calculate audio level to detect if user is speaking
                    import struct
                    if len(mic_chunk) > 0:
                        samples = struct.unpack(f'<{len(mic_chunk)//2}h', mic_chunk)
                        audio_level = (sum(s*s for s in samples) / len(samples)) ** 0.5
                        
                        # If audio level is above threshold, it's a barge-in
                        if audio_level > 1000:  # Threshold for barge-in detection
                            realtime_instance.barge_in = True
                            realtime_instance.interruption_detected = True
                            realtime_instance.audio_quality_metrics["interruption_count"] += 1
                            logging.info("🔄 Barge-in detected in audio stream")
                
                # Check if WebSocket is still connected before sending
                if (socket and not socket.closed and socket.connected and 
                    hasattr(realtime_instance, 'websocket_connected') and realtime_instance.websocket_connected):
                    encoded_chunk = base64.b64encode(mic_chunk).decode('utf-8')
                    try:
                        socket.send({'type': 'input_audio_buffer.append', 'audio': encoded_chunk})
                        # Only log every 10th chunk to reduce noise
                        if hasattr(realtime_instance, '_audio_send_count'):
                            realtime_instance._audio_send_count += 1
                        else:
                            realtime_instance._audio_send_count = 1
                        
                        if realtime_instance._audio_send_count % 10 == 0:
                            logging.info(f'🎤 Sent {realtime_instance._audio_send_count} audio chunks')
                    except Exception as e:
                        logging.error(f"Error sending audio to WebSocket: {e}")
                        # Mark WebSocket as disconnected and stop the audio stream
                        realtime_instance.websocket_connected = False
                        if hasattr(realtime_instance, 'audio_io') and hasattr(realtime_instance.audio_io, 'mic_stream') and realtime_instance.audio_io.mic_stream:
                            realtime_instance.audio_io.mic_stream.stop_stream()
                else:
                    # Only log warning occasionally to reduce noise
                    if not hasattr(realtime_instance, '_last_warning_time') or time.time() - realtime_instance._last_warning_time > 5:
                        logging.warning("WebSocket not connected, skipping audio send")
                        realtime_instance._last_warning_time = time.time()

    def receive_audio(self, audio_chunk):
        logging.info(f'🔊 DEBUG: receive_audio called with {len(audio_chunk)} bytes')
        self.audio_buffer.extend(audio_chunk)
        logging.info(f'🔊 DEBUG: Audio added to buffer, buffer size now: {len(self.audio_buffer)} bytes')
        # Record assistant audio in continuous stream
        self.record_assistant_audio(audio_chunk)
    
    def prefill_audio_buffer(self):
        """Pre-fill the audio buffer with silence to prevent initial underruns"""
        try:
            # Pre-fill with silence to prevent initial buffer underruns
            silence_duration = 0.1  # 100ms of silence
            silence_samples = int(self.rate * silence_duration)
            silence_data = b'\x00' * (silence_samples * 2)  # 2 bytes per sample for 16-bit
            self.audio_buffer.extend(silence_data)
            logging.info(f"🔊 Pre-filled audio buffer with {len(silence_data)} bytes of silence")
        except Exception as e:
            logging.warning(f"⚠️ Could not pre-fill audio buffer: {e}")


class KnowledgeBaseManager:
    """Manages knowledge base retrieval and context injection"""
    
    def __init__(self):
        self.conversation_history = []
        self.last_query_time = 0
        self.query_cooldown = 1.0  # Minimum seconds between knowledge base queries
        
    def extract_query_from_text(self, text):
        """Extract potential query from transcribed text"""
        # Enhanced keyword-based extraction for interview context
        keywords = [
            'experience', 'skills', 'education', 'project', 'work', 'background', 
            'qualification', 'technology', 'programming', 'resume', 'cv',
            'python', 'java', 'javascript', 'ai', 'machine learning', 'deep learning',
            'langchain', 'tensorflow', 'pytorch', 'aws', 'cloud', 'database',
            'react', 'angular', 'vue', 'django', 'flask', 'spring',
            'docker', 'kubernetes', 'git', 'github', 'deployment',
            'tell me about', 'what is your', 'how do you', 'can you explain',
            'describe', 'walk me through', 'show me', 'give me an example'
        ]
        
        text_lower = text.lower()
        if any(keyword in text_lower for keyword in keywords):
            return text.strip()
        return None
    
    def should_query_knowledge_base(self, text):
        """Determine if we should query the knowledge base"""
        current_time = time.time()
        if current_time - self.last_query_time < self.query_cooldown:
            return False
            
        query = self.extract_query_from_text(text)
        return query is not None
    
    def get_context_for_query(self, query):
        """Retrieve context from knowledge base"""
        try:
            logging.info(f"🔍 Querying knowledge base for: {query}")
            result = knowledge_retrieval(query)
            
            if result['status'] == 'success' and result['contexts']:
                contexts = result['contexts']
                context_text = "\n\n".join([
                    f"Context {i+1} (Relevance: {ctx['relevance_score']:.2f}):\n{ctx['content']}"
                    for i, ctx in enumerate(contexts[:2])  # Use top 2 contexts
                ])
                logging.info(f"📚 Retrieved {len(contexts)} contexts from knowledge base")
                return context_text
            else:
                logging.info("📚 No relevant contexts found in knowledge base")
                return None
                
        except Exception as e:
            logging.error(f"❌ Error querying knowledge base: {e}")
            return None


class RealtimeWithKB:
    def __init__(self, api_key, ws_url, interview_type="technical"):
        self.socket = Socket(api_key, ws_url)
        self.audio_io = AudioIO()
        self.kb_manager = KnowledgeBaseManager()
        self.websocket_connected = False
        self.conversation_buffer = ""
        self.is_processing_text = False
        self.interview_type = interview_type
        self.conversation_history = []
        self.asked_questions = set()
        
        # Enhanced short-term memory system
        self.short_term_memory = {
            'recent_context': [],  # Last 5 turns for immediate context
            'key_topics': [],      # Important topics mentioned
            'user_preferences': {}, # User's stated preferences
            'follow_up_needed': [], # Questions that need follow-up
            'conversation_state': 'initial',  # Current conversation state
            'last_user_topic': None,  # Last topic user mentioned
            'response_context': {},   # Context for next response
            'memory_buffer': [],      # Buffer for important information
            'conversation_flow': []   # Track conversation flow
        }
        
        # Initialize metrics collector with realtime model identification
        # Note: LLM-as-a-Judge uses gpt-4o-mini via chat completions API, not the realtime model
        self.metrics = MetricsCollector(model_id='openai.gpt-realtime')
        self.session_id = self._generate_session_id()
        self.start_time = time.time()
        self.audio_recording_buffer = []
        self.transcript_buffer = []
        
        # Model identification for outputs
        self.model_identification = {
            "model_id": "openai.gpt-realtime",
            "model_name": "gpt-realtime",
            "model_version": "latest",
            "provider": "OpenAI",
            "model_type": "speech-to-speech",
            "configuration": {
                "sample_rate_input": 24000,
                "sample_rate_output": 24000,
                "audio_format": "PCM",
                "temperature": 0.7,
                "max_tokens": 4096
            },
            "system_info": {
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "os": f"{os.name} {os.uname().sysname if hasattr(os, 'uname') else 'unknown'}",
                "region": "us-east-1"
            }
        }
        
        # Enhanced conversation state management (Nova Sonic style)
        self.conversation_state = {
            "current_question_index": 0,
            "questions_asked": [],
            "user_responses": [],
            "last_user_response": None,
            "extracted_info": {
                "experience_years": None,
                "programming_languages": [],
                "ai_ml_experience": [],
                "generative_ai_experience": [],
                "cloud_platforms": [],
                "data_processing": [],
                "current_role": None,
                "projects_mentioned": [],
                "technologies_mentioned": []
            },
            "topics_covered": [],
            "adaptive_questions": [],
            "user_frustration_level": 0,
            "conversation_quality": "good"
        }
        
        # Barge-in and interruption handling
        self.barge_in = False
        self.is_speaking = False
        self.interruption_detected = False
        
        # Enhanced audio processing
        self.audio_quality_metrics = {
            "user_audio_levels": [],
            "assistant_audio_levels": [],
            "feedback_incidents": 0,
            "interruption_count": 0
        }
        
        # Checkpoint functionality
        self.checkpoint_dir = None
        self.checkpoint_files = []
        self.checkpoint_interval = 30  # Save checkpoint every 30 seconds
        self.last_checkpoint_time = time.time()
        
        # Periodic transcript saving
        self.last_transcript_save_time = time.time()
        self.transcript_save_interval = 15  # Save transcript every 15 seconds
        
        # Short-term memory is already initialized above - no need to duplicate
        
        # Interview completion flag
        self.interview_completed = False

    def _generate_session_id(self):
        """Generate a unique session ID for this interview session."""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _load_jd_content(self):
        """Load job description content from file."""
        try:
            jd_path = "data/input/JD_Generative_AI.txt"
            if os.path.exists(jd_path):
                with open(jd_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                logging.warning(f"JD file not found at {jd_path}")
                return "Generative AI Engineer position with focus on AI/ML technologies"
        except Exception as e:
            logging.error(f"Error loading JD content: {e}")
            return "Generative AI Engineer position with focus on AI/ML technologies"
    
    def _load_resume_content(self):
        """Load resume content from file."""
        try:
            resume_path = "data/input/Resume_VarunSoni.pdf"
            if os.path.exists(resume_path):
                # For now, return a placeholder since we'd need PDF parsing
                # In a real implementation, you'd parse the PDF here
                return "Resume content available in knowledge base - contains AI/ML experience, Python, and related technologies"
            else:
                logging.warning(f"Resume file not found at {resume_path}")
                return "Resume information available in knowledge base"
        except Exception as e:
            logging.error(f"Error loading resume content: {e}")
            return "Resume information available in knowledge base"

    def _update_conversation_state(self, speaker, text):
        """Update conversation state with speaker and text information."""
        if not hasattr(self, 'conversation_state'):
            return
            
        if speaker == "Assistant":
            # Check if this is a question
            if "?" in text and len(text.strip()) > 10:
                clean_question = text.strip()
                if clean_question not in self.conversation_state["questions_asked"]:
                    self.conversation_state["questions_asked"].append(clean_question)
                    self.conversation_state["current_question_index"] += 1
                    logging.info(f"📝 Question {self.conversation_state['current_question_index']} recorded: {clean_question[:50]}...")
        elif speaker == "User":
            self.conversation_state["user_responses"].append(text)
            self.conversation_state["last_user_response"] = text
            
            # Detect user frustration
            frustration_indicators = ["i don't know", "i can't", "i'm not sure", "this is hard", "i'm confused"]
            if any(indicator in text.lower() for indicator in frustration_indicators):
                self.conversation_state["user_frustration_level"] = self.conversation_state.get("user_frustration_level", 0) + 1
                logging.info(f"⚠️ User frustration detected (level: {self.conversation_state['user_frustration_level']})")

    def _extract_information_from_response(self, response_text):
        """Extract structured information from user responses."""
        if not hasattr(self, 'conversation_state'):
            return
        extracted = self.conversation_state["extracted_info"]
        text_lower = response_text.lower()
        
        # Extract programming languages
        languages = ["python", "java", "javascript", "typescript", "c++", "c#", "go", "rust", "scala", "kotlin", "swift", "php", "ruby", "r", "matlab"]
        for lang in languages:
            if lang in text_lower and lang not in extracted["programming_languages"]:
                extracted["programming_languages"].append(lang)
        
        # Extract AI/ML experience
        ai_terms = ["machine learning", "deep learning", "neural networks", "tensorflow", "pytorch", "scikit-learn", "pandas", "numpy"]
        for term in ai_terms:
            if term in text_lower and term not in extracted["ai_ml_experience"]:
                extracted["ai_ml_experience"].append(term)
        
        # Extract cloud platforms
        cloud_platforms = ["aws", "azure", "gcp", "google cloud", "amazon web services", "microsoft azure"]
        for platform in cloud_platforms:
            if platform in text_lower and platform not in extracted["cloud_platforms"]:
                extracted["cloud_platforms"].append(platform)
        
        # Extract years of experience
        import re
        years_match = re.search(r'(\d+)\s*(?:years?|yrs?)', text_lower)
        if years_match and not extracted["experience_years"]:
            extracted["experience_years"] = int(years_match.group(1))
        
        return extracted

    def _generate_adaptive_question(self):
        """Generate adaptive questions based on conversation state."""
        extracted = self.conversation_state["extracted_info"]
        recent_responses = self.conversation_state.get("user_responses", [])
        
        # Generate follow-up questions based on extracted information
        if extracted["programming_languages"] and len(recent_responses) < 3:
            return f"Great! I see you have experience with {', '.join(extracted['programming_languages'][:2])}. Can you tell me about a challenging project you worked on using these technologies?"
        
        if extracted["ai_ml_experience"] and "project" not in " ".join(recent_responses[-2:]):
            return f"You mentioned experience with {', '.join(extracted['ai_ml_experience'][:2])}. What was the most interesting AI/ML problem you solved?"
        
        if extracted["cloud_platforms"] and "cloud" not in " ".join(recent_responses[-2:]):
            return f"I see you have experience with {', '.join(extracted['cloud_platforms'][:2])}. How do you typically deploy and manage AI models in the cloud?"
        
        # Default adaptive questions
        adaptive_questions = [
            "Can you walk me through your approach to debugging complex AI models?",
            "How do you stay updated with the latest developments in generative AI?",
            "What's your experience with model optimization and performance tuning?",
            "How do you handle data privacy and security in AI applications?",
            "Can you describe a time when you had to explain a complex AI concept to non-technical stakeholders?"
        ]
        
        # Return a question that hasn't been asked yet
        for question in adaptive_questions:
            if question not in self.conversation_state["questions_asked"]:
                return question
        
        return None

    def _setup_checkpoint_directory(self):
        """Setup session-specific checkpoint directory."""
        try:
            # Create checkpoint directory structure
            self.checkpoint_dir = f"data/outputs/evaluations/.checkpoints/{self.session_id}"
            os.makedirs(self.checkpoint_dir, exist_ok=True)
            logging.info(f"📁 Checkpoint directory created: {self.checkpoint_dir}")
        except Exception as e:
            logging.error(f"⚠️ Error setting up checkpoint directory: {e}")
            # Fallback checkpoint directory
            self.checkpoint_dir = f"data/outputs/evaluations/.checkpoints/fallback_{int(time.time())}"
            os.makedirs(self.checkpoint_dir, exist_ok=True)

    def _save_checkpoint(self):
        """Save current session state as checkpoint."""
        try:
            if not self.checkpoint_dir:
                self._setup_checkpoint_directory()
            
            current_time = time.time()
            checkpoint_data = {
                "checkpoint": True,
                "timestamp": current_time,
                "session_id": self.session_id,
                "conversation_state": self.conversation_state,
                "conversation_history": self.conversation_history[-10:],  # Last 10 turns
                "short_term_memory": self.short_term_memory,
                "audio_quality_metrics": self.audio_quality_metrics,
                "transcript_buffer": self.transcript_buffer[-10:],  # Last 10 transcript entries
                "session_duration": current_time - self.start_time,
                "model_identification": self.model_identification
            }
            
            # Save to session-specific checkpoint directory
            checkpoint_file = os.path.join(self.checkpoint_dir, f"checkpoint_{int(current_time)}.json")
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2, cls=SafeJSONEncoder)
            
            # Track checkpoint file for cleanup
            self.checkpoint_files.append(checkpoint_file)
            self.last_checkpoint_time = current_time
            
            logging.info(f"💾 Checkpoint saved: {checkpoint_file}")
            
        except Exception as e:
            logging.error(f"⚠️ Error saving checkpoint: {e}")

    def _cleanup_checkpoints(self):
        """Clean up checkpoint files after interview completion."""
        try:
            if not self.checkpoint_dir or not os.path.exists(self.checkpoint_dir):
                return
            
            checkpoint_count = len(self.checkpoint_files)
            
            # Remove all checkpoint files
            for checkpoint_file in self.checkpoint_files:
                try:
                    if os.path.exists(checkpoint_file):
                        os.remove(checkpoint_file)
                except Exception as e:
                    logging.error(f"⚠️ Error removing checkpoint {checkpoint_file}: {e}")
            
            # Try to remove the checkpoint directory if it's empty
            try:
                if os.path.exists(self.checkpoint_dir):
                    os.rmdir(self.checkpoint_dir)
                    logging.info(f"🧹 Cleaned up checkpoint directory: {self.checkpoint_dir}")
            except OSError:
                logging.info(f"📁 Checkpoint directory kept (not empty): {self.checkpoint_dir}")
            
            if checkpoint_count > 0:
                logging.info(f"🧹 Cleaned up {checkpoint_count} checkpoint files")
                
        except Exception as e:
            logging.error(f"⚠️ Error during checkpoint cleanup: {e}")

    def _update_short_term_memory(self, speaker, text):
        """Update short-term memory with conversation context."""
        try:
            # Add to recent context (keep last 5 turns)
            self.short_term_memory["recent_context"].append({
                "speaker": speaker,
                "text": text,
                "timestamp": time.time()
            })
            
            # Keep only last 5 conversation turns
            if len(self.short_term_memory["recent_context"]) > 5:
                self.short_term_memory["recent_context"] = self.short_term_memory["recent_context"][-5:]
            
            # Extract key topics from the conversation
            if speaker == "User":
                # Extract important topics from user responses
                key_words = ["project", "experience", "technology", "skill", "challenge", "problem", "solution"]
                for word in key_words:
                    if word in text.lower() and word not in self.short_term_memory["key_topics"]:
                        self.short_term_memory["key_topics"].append(word)
                
                # Track conversation flow
                self.short_term_memory["conversation_flow"].append({
                    "topic": "user_response",
                    "timestamp": time.time(),
                    "length": len(text.split())
                })
            
            # Update memory buffer with important information
            if len(text) > 50:  # Only store substantial responses
                self.short_term_memory["memory_buffer"].append({
                    "speaker": speaker,
                    "text": text[:200] + "..." if len(text) > 200 else text,  # Truncate long text
                    "timestamp": time.time()
                })
                
                # Keep only last 10 memory entries
                if len(self.short_term_memory["memory_buffer"]) > 10:
                    self.short_term_memory["memory_buffer"] = self.short_term_memory["memory_buffer"][-10:]
                    
        except Exception as e:
            logging.error(f"⚠️ Error updating short-term memory: {e}")

    def _get_conversation_context(self):
        """Get recent conversation context for better responses."""
        try:
            context_parts = []
            
            # Add recent conversation turns
            if self.short_term_memory["recent_context"]:
                context_parts.append("Recent conversation:")
                for turn in self.short_term_memory["recent_context"][-3:]:  # Last 3 turns
                    context_parts.append(f"{turn['speaker']}: {turn['text'][:100]}...")
            
            # Add key topics
            if self.short_term_memory["key_topics"]:
                context_parts.append(f"Key topics discussed: {', '.join(self.short_term_memory['key_topics'][-5:])}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logging.error(f"⚠️ Error getting conversation context: {e}")
            return ""

    def _check_checkpoint_interval(self):
        """Check if it's time to save a checkpoint."""
        current_time = time.time()
        if current_time - self.last_checkpoint_time >= self.checkpoint_interval:
            self._save_checkpoint()
            self.last_checkpoint_time = current_time
    
    def _check_transcript_save_interval(self):
        """Check if it's time to save transcript periodically"""
        current_time = time.time()
        if current_time - self.last_transcript_save_time >= self.transcript_save_interval:
            self._save_periodic_transcript()
            self.last_transcript_save_time = current_time
    
    def _save_periodic_transcript(self):
        """Save transcript periodically to prevent data loss"""
        if not self.transcript_buffer:
            return
            
        try:
            timestamp = int(time.time())
            transcript_path = f"data/outputs/transcripts/transcript_realtime_{timestamp}_periodic.json"
            os.makedirs(os.path.dirname(transcript_path), exist_ok=True)
            
            transcript_data = {
                "session_id": self.session_id,
                "timestamp": timestamp,
                "interview_type": self.interview_type,
                "conversation": self.transcript_buffer,
                "total_exchanges": len(self.transcript_buffer),
                "duration_seconds": time.time() - self.start_time
            }
            
            with open(transcript_path, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, indent=2, cls=SafeJSONEncoder)
            
            logging.info(f"📝 Periodic transcript saved: {transcript_path}")
        except Exception as e:
            logging.error(f"Error saving periodic transcript: {e}")
    
    def _check_interview_completion(self, response_text):
        """Check if the interview should be completed"""
        completion_phrases = [
            "thank you for your time",
            "that concludes our interview", 
            "interview is complete",
            "we're done here",
            "end the interview",
            "no more questions",
            "interview is over"
        ]
        
        response_lower = response_text.lower()
        if any(phrase in response_lower for phrase in completion_phrases):
            logging.info("🎯 Interview completion detected - ending session")
            # Set a flag to end the session gracefully
            self.interview_completed = True

    def start(self):
        # Start metrics collection
        self.metrics.start_session()
        logging.info(f"🚀 Starting Realtime AI session: {self.session_id}")
        
        # Setup checkpoint directory
        self._setup_checkpoint_directory()
        
        self.socket.on_msg = self.handle_message
        self.socket.realtime_instance = self  # Set reference for WebSocket error handling
        self.socket.connect()
        self.websocket_connected = True

        # Load actual JD and Resume content for proper question generation
        jd_text = self._load_jd_content()
        resume_text = self._load_resume_content()
        
        # Generate structured questions to prevent hallucination
        structured_questions = QuestionGeneration.generate_technical_questions(
            jd_text=jd_text,
            resume_text=resume_text,
            role_type="Generative AI Engineer"
        )
        
        # Record question alignment metrics (disabled to avoid API calls during startup)
        # self.metrics.record_question_alignment_metrics(
        #     questions=structured_questions,
        #     jd_text=jd_text,
        #     resume_text=resume_text,
        #     role_type="Generative AI Engineer"
        # )
        
        # Follow OpenAI Realtime API prompt guidelines for optimal performance
        instructions = """# Role and Objective
You are a professional technical interviewer conducting a live interview for a Generative AI Engineer position. Your goal is to assess the candidate's technical skills, experience, and fit for the role through structured questioning.

# Personality & Tone
## Personality
- Professional, knowledgeable, and encouraging interviewer
- Calm and approachable expert in AI/ML technologies

## Tone
- Warm, concise, confident, never condescending
- Maintain professional interview atmosphere

# Response Guidelines
## Length
- 1-2 sentences per turn
- Keep questions focused and specific

## Pacing
- Deliver your audio response at a natural pace
- Allow time for the candidate to respond
- Do not rush through questions

## Language
- The conversation will be only in English
- Use clear, professional language appropriate for technical interviews

## Variety
- Do not repeat the same question twice
- Vary your follow-up questions based on candidate responses
- Adapt questions based on their background

# Interview Behavior
## Core Rules
- ONLY ask questions - NEVER provide explanations or answers
- If candidate says "I don't know", acknowledge it and ask the next question
- You are an INTERVIEWER, not a teacher
- Focus on assessing their experience and skills

## Question Flow
- Start with background and experience questions
- Progress to technical depth questions
- Ask about specific projects and technologies
- End with candidate questions about the role

# Opening
Begin with: "Hello! I'm here to conduct your technical interview for the Generative AI Engineer position. Let's start with your background. Can you tell me about your experience with AI and machine learning technologies?"""

        # Send optimized session configuration following OpenAI Realtime API best practices
        self.socket.send({
            'type': 'session.update',
            'session': {
                'instructions': instructions,
                'input_audio_transcription': {
                    'model': 'whisper-1',
                },
                'modalities': ['audio', 'text'],
                'turn_detection': {
                    'type': 'server_vad',
                    'threshold': 0.5,
                    'prefix_padding_ms': 300,
                    'silence_duration_ms': 500
                },
                'inference_config': {
                    'max_tokens': 150,
                    'temperature': 0.7,
                    'top_p': 0.9
                }
            }
        })

        # Create response for audio output
        self.socket.send({'type': 'response.create'})
        
        # Get context-aware prompt for enhanced memory
        context_prompt = self.get_context_aware_prompt()
        
        # Send initial system message to ensure proper interview behavior
        initial_message = {
            'type': 'conversation.item.create',
            'item': {
                'type': 'message',
                'role': 'system',
                'content': [
                    {
                        'type': 'input_text',
                        'text': f"""CRITICAL INTERVIEWER INSTRUCTIONS:
You are conducting a technical interview for a {self.interview_type} position. 

STRICT RULES:
1. ONLY ask questions - NEVER provide explanations, definitions, or educational content
2. If candidate says "I don't know", acknowledge it and ask the NEXT question
3. Do NOT explain concepts or provide answers
4. Your role is to ASSESS, not TEACH
5. Start with a warm welcome and ask your first technical question
6. Use conversation context to ask relevant follow-up questions

CONVERSATION CONTEXT:
{context_prompt}
6. Be professional, encouraging, and ask one question at a time

REMEMBER: You are an INTERVIEWER, not a teacher!"""
                    }
                ]
            }
        }
        self.socket.send(initial_message)

        audio_send_thread = threading.Thread(target=self.audio_io.send_mic_audio, args=(self.socket, self), daemon=True)
        audio_send_thread.start()

        # Pre-fill audio buffer to prevent initial underruns
        self.audio_io.prefill_audio_buffer()
        
        self.audio_io.start_streams()
        
        # Start recording
        self.audio_io.start_recording()

    def handle_message(self, message):
        event_type = message.get('type')
        logging.info(f'Received message type: {event_type}')
        
        # Debug logging for all message structures to identify transcript issues
        if event_type in ['conversation.item.input_audio_transcription.completed', 'input_audio_buffer.committed', 'input_audio_buffer.speech_stopped']:
            logging.info(f'🔍 DEBUG - Full message structure for {event_type}: {json.dumps(message, indent=2, default=str)}')

        if event_type == 'response.audio.delta':
            logging.info(f'🔊 DEBUG: Processing response.audio.delta message: {message}')
            audio_content = base64.b64decode(message['delta'])
            logging.info(f'🔊 DEBUG: Decoded audio content: {len(audio_content)} bytes')
            self.audio_io.receive_audio(audio_content)
            logging.info(f'🔊 DEBUG: Audio added to buffer, buffer size now: {len(self.audio_io.audio_buffer)} bytes')
            # Record audio for metrics and output
            self.audio_recording_buffer.append(audio_content)
            self.is_speaking = True
            logging.info(f'Received {len(audio_content)} bytes of audio data.')

        elif event_type == 'response.audio.done':
            logging.info('AI finished speaking.')
            self.is_speaking = False
            # Record comprehensive TTS metrics when AI finishes speaking
            if self.audio_recording_buffer:
                combined_audio = b''.join(self.audio_recording_buffer)
                
                # Record TTS metrics
                self.metrics.record_tts_metrics(combined_audio, self.conversation_buffer)
                
                # Record enhanced speech metrics for TTS output
                self.metrics.record_enhanced_speech_metrics(
                    audio_data=combined_audio,
                    transcript=self.conversation_buffer,
                    ground_truth=None
                )
                
                # Record audio intelligibility metrics
                self.metrics.record_audio_intelligibility_metrics(combined_audio)
                
                # Record performance metrics
                self.metrics.record_performance_metrics(
                    first_token_time=0.5,  # Placeholder
                    end_time=1.0,  # Placeholder
                    input_tokens=len(self.conversation_buffer.split()),
                    output_tokens=len(self.conversation_buffer.split()),
                    context_length=len(self.conversation_buffer)
                )
                
                self.audio_recording_buffer = []

        
        elif event_type == 'input_audio_buffer.speech_started':
            logging.info('🎤 User started speaking')
            
        elif event_type == 'input_audio_buffer.audio_added':
            # Record user audio data - handled by continuous recording system
            logging.info('🎤 User audio added to buffer')
            
        elif event_type == 'input_audio_buffer.speech_stopped':
            logging.info('🎤 User stopped speaking')
            # Commit the audio buffer for processing
            self.socket.send({'type': 'input_audio_buffer.commit'})
            
        elif event_type == 'input_audio_buffer.committed':
            logging.info('🎤 User audio committed for processing')
            # Check for barge-in (user spoke while AI was speaking)
            if self.is_speaking:
                self.barge_in = True
                self.interruption_detected = True
                self.audio_quality_metrics["interruption_count"] += 1
                
                # Record barge-in metrics
                self.metrics.record_barge_in_metrics(
                    user_interrupted=True,
                    interruption_point=time.time()
                )
                
                # Record enhanced no-ground-truth metrics
                self.metrics.record_enhanced_no_ground_truth_metrics(
                    interruption_detected=True,
                    user_satisfaction_proxy=0.7  # Placeholder
                )
                
                logging.info("🔄 Barge-in detected - user interrupted AI")
        
        elif event_type == 'conversation.item.input_audio_transcription.completed':
            # Handle transcribed text from user
            logging.info(f"🔍 USER TRANSCRIPTION EVENT RECEIVED")
            logging.info(f"🔍 Debug - Full message structure: {message}")
            
            # Get the item from the message (following working openai_realtime.py pattern)
            item = message.get('item', {})
            logging.info(f"🔍 DEBUG: Audio transcription completed: {json.dumps(item, indent=2)}")
            
            # Check if this is a user transcription
            if item.get('role') == 'user':
                transcribed_text = item.get('transcript', '')
                if not transcribed_text:
                    # Try alternative transcript locations based on OpenAI docs
                    if 'transcription' in item and 'transcript' in item['transcription']:
                        transcribed_text = item['transcription']['transcript']
                    elif 'content' in item:
                        transcribed_text = item['content']
                    elif 'text' in item:
                        transcribed_text = item['text']
            
                if transcribed_text and len(transcribed_text.strip()) > 0:
                    logging.info(f'👤 User (transcription completed): {transcribed_text}')
                    
                    # Update short-term memory with user input
                    self.update_short_term_memory(transcribed_text)
                    
                    # Record user transcript
                    self.conversation_history.append({'role': 'user', 'content': transcribed_text})
                    self.transcript_buffer.append({
                        'role': 'user',
                        'content': transcribed_text,
                        'timestamp': time.time()
                    })
                    
                    # Update conversation state
                    self._update_conversation_state("User", transcribed_text)
                    if hasattr(self, 'conversation_state'):
                        self._extract_information_from_response(transcribed_text)
                    
                    # Record comprehensive user turn metrics
                    self.metrics.record_user_turn(
                        audio_data=b"",  # We don't have raw audio here
                        transcript=transcribed_text,
                        timestamp=time.time()
                    )
                    
                    # Record ASR metrics
                    self.metrics.record_asr_metrics(
                        transcript=transcribed_text,
                        confidence_score=0.9,  # Placeholder - would get from ASR
                        ground_truth=None  # No ground truth available
                    )
                    
                    # Record enhanced speech metrics
                    self.metrics.record_enhanced_speech_metrics(
                        audio_data=b"",  # Placeholder
                        transcript=transcribed_text,
                        ground_truth=None
                    )
                    
                    # Process user input for knowledge base queries
                    self.process_user_input(transcribed_text)
                    
                    # Update short-term memory
                    self._update_short_term_memory("User", transcribed_text)
                    
                    # Check for checkpoint interval
                    self._check_checkpoint_interval()
                    
                    # Check for periodic transcript saving
                    self._check_transcript_save_interval()
                else:
                    logging.warning(f"⚠️ Empty or missing transcript in user transcription event: {message}")
                    # Add a placeholder to track that user spoke but transcript failed
                    self.transcript_buffer.append({
                        'role': 'user',
                        'content': '[TRANSCRIPT_FAILED]',
                        'timestamp': time.time()
                    })
            else:
                logging.info(f"🔍 Non-user transcription event or no role specified: {item}")

        elif event_type == 'conversation.item.output_audio_transcription.completed':
            # Handle AI response transcription (for logging)
            logging.info(f"🔍 Debug - AI response message structure: {message}")
            ai_response = message.get('transcript', '')
            if not ai_response:
                # Try alternative transcript locations based on OpenAI docs
                if 'item' in message and 'transcript' in message['item']:
                    ai_response = message['item']['transcript']
                elif 'transcription' in message and 'transcript' in message['transcription']:
                    ai_response = message['transcription']['transcript']
            
            if ai_response:
                logging.info(f'🤖 AI responded: {ai_response}')
                
                # Update short-term memory with AI response
                # Get the last user input to pair with this response
                last_user_input = None
                if self.conversation_history and self.conversation_history[-1]['role'] == 'user':
                    last_user_input = self.conversation_history[-1]['content']
                
                # Update memory with the complete turn
                self.update_short_term_memory(last_user_input, ai_response)
                
                self.conversation_history.append({'role': 'assistant', 'content': ai_response})
                self.transcript_buffer.append({'role': 'assistant', 'content': ai_response, 'timestamp': time.time()})
                
                # Update conversation state
                self._update_conversation_state("Assistant", ai_response)
                
                # Record comprehensive assistant turn metrics
                self.metrics.record_assistant_turn(
                    response=ai_response,
                    context_used="interview_context",
                    timestamp=time.time()
                )
                
                # Record TTS metrics
                self.metrics.record_tts_metrics(
                    audio_output=b"",  # Placeholder - would get from TTS
                    text_input=ai_response
                )
                
                # Record generation quality metrics
                self.metrics.record_generation_quality(
                    response=ai_response,
                    user_input=self.conversation_state.get("last_user_response", ""),
                    context="interview_context",
                    appropriateness_score=None  # Will be calculated
                )
                
                # Record conversation flow metrics
                self.metrics.record_conversation_flow_metrics(
                    turn_type="assistant",
                    interruption_occurred=self.interruption_detected,
                    context_maintained=True
                )
                
                # Update short-term memory
                self._update_short_term_memory("Assistant", ai_response)
                
                # Check for interview completion
                self._check_interview_completion(ai_response)

        elif event_type == 'response.audio_transcript.delta':
            # Handle streaming transcription
            if 'delta' in message:
                self.conversation_buffer += message['delta']
                logging.info(f'📝 Streaming transcript: {message["delta"]}')

        elif event_type == 'response.audio_transcript.done':
            # Handle completed transcription
            if self.conversation_buffer.strip():
                logging.info(f'📝 Complete AI transcript: {self.conversation_buffer}')
                self.conversation_history.append({'role': 'assistant', 'content': self.conversation_buffer})
                self.transcript_buffer.append({'role': 'assistant', 'content': self.conversation_buffer, 'timestamp': time.time()})
                self.conversation_buffer = ""

        elif event_type == 'response.tool_call':
            # Handle tool calls from the AI
            tool_call = message.get('tool_call', {})
            tool_name = tool_call.get('name', '')
            tool_call_id = tool_call.get('id', '')
            
            logging.info(f"🔧 Tool call received: {tool_name} with ID: {tool_call_id}")
            
            if tool_name == 'retrieve_knowledge':
                # Extract query from tool call
                arguments = tool_call.get('arguments', {})
                query = arguments.get('query', '')
                
                if query:
                    logging.info(f"🔍 AI requested knowledge retrieval for: {query}")
                    
                    # Perform knowledge retrieval using the knowledge base module
                    try:
                        from src.agents.realtime.knowledgebase import knowledge_retrieval
                        
                        # Call the knowledge retrieval function
                        kb_result = knowledge_retrieval(query)
                        
                        if kb_result['status'] == 'success' and kb_result['contexts']:
                            # Format the retrieved contexts
                            contexts = kb_result['contexts']
                            context_text = "\n\n".join([
                                f"Context {i+1} (Relevance: {ctx['relevance_score']:.2f}):\n{ctx['content']}"
                                for i, ctx in enumerate(contexts[:3])  # Use top 3 contexts
                            ])
                            
                            # Send tool result back to AI
                            self.socket.send({
                                'type': 'response.tool_call.result',
                                'tool_call_id': tool_call_id,
                                'result': context_text
                            })
                            
                            # Record comprehensive RAG metrics
                            self.metrics.record_rag_retrieval_metrics(
                                query=query,
                                retrieved_docs=contexts,
                                retrieval_time=0.1,  # Placeholder - would measure actual time
                                relevance_scores=[ctx['relevance_score'] for ctx in contexts]
                            )
                            
                            # Record context utilization metrics
                            self.metrics.record_rag_context_utilization_metrics(
                                retrieved_context=context_text,
                                response="",  # Will be filled when AI responds
                                context_used=context_text,
                                context_window_size=4096
                            )
                            
                            logging.info(f"📚 Retrieved {len(contexts)} contexts from knowledge base")
                            
                        else:
                            # No relevant contexts found
                            self.socket.send({
                                'type': 'response.tool_call.result',
                                'tool_call_id': tool_call_id,
                                'result': "No relevant information found in the knowledge base for this query."
                            })
                            
                            # Record failed retrieval
                            self.metrics.record_rag_error_metrics(
                                retrieval_success=False,
                                generation_success=True,
                                fallback_used=True
                            )
                            
                            logging.info("📚 No relevant contexts found in knowledge base")
                        
                    except Exception as e:
                        logging.error(f"Error in knowledge retrieval: {e}")
                        self.socket.send({
                            'type': 'response.tool_call.result',
                            'tool_call_id': tool_call_id,
                            'result': f"Error retrieving knowledge: {str(e)}"
                        })
                        
                        # Record error metrics
                        self.metrics.record_rag_error_metrics(
                            retrieval_success=False,
                            generation_success=False,
                            fallback_used=True
                        )
                else:
                    logging.warning("Empty query in knowledge retrieval tool call")
                    self.socket.send({
                        'type': 'response.tool_call.result',
                        'tool_call_id': tool_call_id,
                        'result': "No query provided for knowledge retrieval."
                    })
            
            else:
                logging.warning(f"Unknown tool call: {tool_name}")
                self.socket.send({
                    'type': 'response.tool_call.result',
                    'tool_call_id': tool_call_id,
                    'result': f"Unknown tool: {tool_name}"
                })

    def process_user_input(self, text):
        """Process user input and potentially inject knowledge base context"""
        if self.is_processing_text:
            return
            
        self.is_processing_text = True
        
        try:
            # Check if we should query the knowledge base
            if self.kb_manager.should_query_knowledge_base(text):
                context = self.kb_manager.get_context_for_query(text)
                
                if context:
                    # Record RAG metrics
                    self.metrics.record_rag_metrics(
                        query=text,
                        retrieved_context=context,
                        relevance_score=0.8  # Default relevance score
                    )
                    
                    # Inject context into the conversation
                    self.inject_context_to_conversation(context, text)
                    
        except Exception as e:
            logging.error(f"Error processing user input: {e}")
        finally:
            self.is_processing_text = False

    def inject_context_to_conversation(self, context, user_query):
        """Inject knowledge base context into the conversation with anti-hallucination measures"""
        try:
            # Get recent conversation context from short-term memory
            conversation_context = self._get_conversation_context()
            
            # Create enhanced context message with anti-hallucination guidance
            enhanced_context = f"User Query: {user_query}\n\n"
            
            if conversation_context:
                enhanced_context += f"Recent Conversation Context:\n{conversation_context}\n\n"
            
            enhanced_context += f"Relevant Context from Knowledge Base:\n{context}\n\n"
            enhanced_context += """ANTI-HALLUCINATION GUIDANCE:
- Use ONLY the information provided above to ask follow-up questions
- Do NOT make up or assume any technologies, companies, or requirements not mentioned
- Stay strictly within the scope of the JD and Resume content
- If the candidate mentions something not in the context, acknowledge it but don't elaborate beyond what's provided
- Ask questions only about topics clearly mentioned in the knowledge base content
- Follow the structured question framework provided in your instructions"""
            
            # Create a system message with enhanced context
            context_message = {
                'type': 'conversation.item.create',
                'item': {
                    'type': 'message',
                    'role': 'system',
                    'content': [
                        {
                            'type': 'input_text',
                            'text': enhanced_context
                        }
                    ]
                }
            }
            
            self.socket.send(context_message)
            logging.info("💡 Injected enhanced knowledge base context with conversation memory")
            
        except Exception as e:
            logging.error(f"Error injecting context: {e}")

    def get_conversation_context(self):
        """Get recent conversation context for better responses"""
        if len(self.conversation_history) < 2:
            return ""
        
        recent_context = []
        for msg in self.conversation_history[-4:]:  # Last 4 messages
            role = "User" if msg['role'] == 'user' else "AI"
            recent_context.append(f"{role}: {msg['content']}")
        
        return "\n".join(recent_context)

    def update_short_term_memory(self, user_input, ai_response=None):
        """Update short-term memory with new conversation turn"""
        # Ensure short_term_memory is properly initialized
        if not hasattr(self, 'short_term_memory'):
            self.short_term_memory = {
                'recent_context': [],
                'key_topics': [],
                'user_preferences': {},
                'follow_up_needed': [],
                'conversation_state': 'initial',
                'last_user_topic': None,
                'response_context': {},
                'memory_buffer': [],
                'conversation_flow': []
            }
        
        # Add to recent context (keep last 5 turns)
        if 'recent_context' not in self.short_term_memory:
            self.short_term_memory['recent_context'] = []
            
        self.short_term_memory['recent_context'].append({
            'user': user_input,
            'ai': ai_response,
            'timestamp': time.time()
        })
        
        # Keep only last 5 turns
        if len(self.short_term_memory['recent_context']) > 5:
            self.short_term_memory['recent_context'] = self.short_term_memory['recent_context'][-5:]
        
        # Extract key topics from user input
        self._extract_key_topics(user_input)
        
        # Update conversation state
        self._update_conversation_state(user_input, ai_response)
        
        # Check for follow-up needs
        self._check_follow_up_needs(user_input, ai_response)

    def _extract_key_topics(self, user_input):
        """Extract key topics and preferences from user input"""
        if not user_input:
            return
            
        # Simple keyword extraction (could be enhanced with NLP)
        key_terms = []
        user_lower = user_input.lower()
        
        # Technical skills
        tech_skills = ['python', 'machine learning', 'ai', 'data science', 'nlp', 'computer vision', 
                      'deep learning', 'tensorflow', 'pytorch', 'aws', 'cloud', 'docker', 'kubernetes']
        
        for skill in tech_skills:
            if skill in user_lower:
                key_terms.append(skill)
        
        # Experience indicators
        exp_indicators = ['experience', 'worked', 'project', 'years', 'team', 'company', 'role']
        for indicator in exp_indicators:
            if indicator in user_lower:
                key_terms.append(indicator)
        
        # Add to key topics if not already present
        for term in key_terms:
            if term not in self.short_term_memory['key_topics']:
                self.short_term_memory['key_topics'].append(term)
        
        # Update last user topic
        if key_terms:
            self.short_term_memory['last_user_topic'] = key_terms[-1]

    def _update_conversation_state(self, user_input, ai_response):
        """Update conversation state based on current turn"""
        if not user_input:
            return
            
        # Ensure conversation_state exists in short_term_memory
        if 'conversation_state' not in self.short_term_memory:
            self.short_term_memory['conversation_state'] = 'initial'
            
        user_lower = user_input.lower()
        
        # State transitions
        if 'hello' in user_lower or 'hi' in user_lower:
            self.short_term_memory['conversation_state'] = 'greeting'
        elif any(word in user_lower for word in ['experience', 'worked', 'project']):
            self.short_term_memory['conversation_state'] = 'experience_discussion'
        elif any(word in user_lower for word in ['skill', 'technology', 'programming']):
            self.short_term_memory['conversation_state'] = 'skills_discussion'
        elif any(word in user_lower for word in ['challenge', 'problem', 'difficult']):
            self.short_term_memory['conversation_state'] = 'challenge_discussion'
        elif any(word in user_lower for word in ['question', 'ask', 'wonder']):
            self.short_term_memory['conversation_state'] = 'question_phase'

    def _check_follow_up_needs(self, user_input, ai_response):
        """Check if follow-up questions are needed based on user response"""
        if not user_input:
            return
            
        user_lower = user_input.lower()
        
        # Check for incomplete responses
        incomplete_indicators = ['i think', 'maybe', 'not sure', 'kind of', 'sort of']
        if any(indicator in user_lower for indicator in incomplete_indicators):
            self.short_term_memory['follow_up_needed'].append('clarification')
        
        # Check for specific mentions that need follow-up
        if 'project' in user_lower and 'details' not in user_lower:
            self.short_term_memory['follow_up_needed'].append('project_details')
        
        if 'team' in user_lower and 'size' not in user_lower:
            self.short_term_memory['follow_up_needed'].append('team_size')

    def get_context_aware_prompt(self):
        """Generate context-aware prompt based on short-term memory"""
        context_parts = []
        
        # Ensure short_term_memory is properly initialized
        if not hasattr(self, 'short_term_memory'):
            self.short_term_memory = {
                'recent_context': [],
                'key_topics': [],
                'user_preferences': {},
                'follow_up_needed': [],
                'conversation_state': 'initial',
                'last_user_topic': None,
                'response_context': {},
                'memory_buffer': [],
                'conversation_flow': []
            }
        
        # Add recent context
        if self.short_term_memory.get('recent_context'):
            context_parts.append("Recent conversation context:")
            for turn in self.short_term_memory['recent_context'][-3:]:  # Last 3 turns
                if turn.get('user'):
                    context_parts.append(f"User: {turn['user']}")
                if turn.get('ai'):
                    context_parts.append(f"AI: {turn['ai']}")
        
        # Add key topics
        if self.short_term_memory.get('key_topics'):
            topics = ", ".join(self.short_term_memory['key_topics'][-5:])  # Last 5 topics
            context_parts.append(f"Key topics discussed: {topics}")
        
        # Add conversation state
        state = self.short_term_memory.get('conversation_state', 'initial')
        context_parts.append(f"Current conversation state: {state}")
        
        # Add follow-up needs
        if self.short_term_memory.get('follow_up_needed'):
            follow_ups = ", ".join(self.short_term_memory['follow_up_needed'][-3:])
            context_parts.append(f"Follow-up needed: {follow_ups}")
        
        return "\n".join(context_parts)

    def stop(self):
        """Stop the realtime session and save all outputs"""
        try:
            logging.info('🛑 Stopping Realtime with KB session...')
            
            # Mark WebSocket as disconnected to stop audio sending
            self.websocket_connected = False
            
            # Set stop event to terminate threads
            if hasattr(self, 'socket') and self.socket:
                self.socket._stop_event.set()
            
            # Save final checkpoint before cleanup
            self._save_final_checkpoint()
            
            # End metrics collection
            self.metrics.end_session()
            
            # Save final metrics and outputs
            self.save_session_outputs()
            
            # Stop audio components first
            logging.info('🔇 Stopping audio streams...')
            if hasattr(self, 'audio_io') and self.audio_io:
                self.audio_io.stop_recording()
                # Save recording immediately to prevent data loss
                if hasattr(self.audio_io, 'recorded_audio') and self.audio_io.recorded_audio:
                    timestamp = int(time.time())
                    audio_path = f"data/outputs/recordings/recording_realtime_{timestamp}.wav"
                    os.makedirs(os.path.dirname(audio_path), exist_ok=True)
                    self.audio_io.save_recording(audio_path)
                    logging.info(f"🎵 Emergency audio save: {audio_path}")
                self.audio_io.stop_streams()
            
            # Close WebSocket connection
            logging.info('🔌 Closing WebSocket connection...')
            if hasattr(self, 'socket') and self.socket:
                try:
                    self.socket.kill()
                except Exception as e:
                    logging.warning(f"Error closing socket: {e}")
            
            # Clean up checkpoint files
            self._cleanup_checkpoints()
            
            # Force cleanup of any remaining resources
            import gc
            gc.collect()
            
            logging.info(f"✅ Session {self.session_id} completed and outputs saved")
            
        except Exception as e:
            logging.error(f"Error during session stop: {e}")
        finally:
            # Ensure we always try to exit cleanly
            logging.info("🏁 Session stop completed")

    def _save_final_checkpoint(self):
        """Save a final checkpoint before cleanup."""
        try:
            if not self.checkpoint_dir:
                return
            
            final_checkpoint = {
                "final_checkpoint": True,
                "timestamp": time.time(),
                "session_id": self.session_id,
                "conversation_state": self.conversation_state,
                "conversation_history": self.conversation_history,
                "short_term_memory": self.short_term_memory,
                "audio_quality_metrics": self.audio_quality_metrics,
                "transcript_buffer": self.transcript_buffer,
                "session_duration": time.time() - self.start_time,
                "model_identification": self.model_identification,
                "total_questions_asked": len(self.conversation_state["questions_asked"]),
                "total_user_responses": len(self.conversation_state["user_responses"]),
                "barge_in_count": self.audio_quality_metrics["interruption_count"],
                "user_frustration_level": self.conversation_state.get("user_frustration_level", 0)
            }
            
            # Save final checkpoint
            final_file = os.path.join(self.checkpoint_dir, "final_checkpoint.json")
            with open(final_file, 'w') as f:
                json.dump(final_checkpoint, f, indent=2, cls=SafeJSONEncoder)
            
            logging.info(f"💾 Final checkpoint saved: {final_file}")
            
        except Exception as e:
            logging.error(f"⚠️ Error saving final checkpoint: {e}")

    def save_session_outputs(self):
        """Save all session outputs following Nova naming patterns with multiple fallback strategies"""
        success = False
        timestamp = int(time.time())
        base_filename = f"realtime_{timestamp}"
        
        # Strategy 1: Try to save comprehensive outputs
        try:
            success = self._save_comprehensive_outputs(base_filename, timestamp)
        except Exception as e:
            logging.error(f"⚠️ Strategy 1 failed: {e}")
        
        # Strategy 2: Try to save basic outputs only
        if not success:
            try:
                success = self._save_basic_outputs(base_filename, timestamp)
            except Exception as e:
                logging.error(f"⚠️ Strategy 2 failed: {e}")
        
        # Strategy 3: Try to save minimal outputs
        if not success:
            try:
                success = self._save_minimal_outputs(base_filename, timestamp)
            except Exception as e:
                logging.error(f"⚠️ Strategy 3 failed: {e}")
        
        # Strategy 4: Save to backup location
        if not success:
            try:
                success = self._save_backup_outputs(timestamp)
            except Exception as e:
                logging.error(f"⚠️ Strategy 4 failed: {e}")
        
        if success:
            logging.info("✅ Session outputs saved successfully!")
        else:
            logging.error("❌ All output saving strategies failed!")
    
    def _save_comprehensive_outputs(self, base_filename, timestamp):
        """Strategy 1: Save comprehensive outputs"""
        # Ensure output directories exist
        os.makedirs("data/outputs/recordings", exist_ok=True)
        os.makedirs("data/outputs/transcripts", exist_ok=True)
        os.makedirs("data/outputs/evaluations/metrics", exist_ok=True)
        os.makedirs("data/outputs/evaluations/summaries", exist_ok=True)
        
        # Save transcript
        transcript_data = {
            "session_id": self.session_id,
            "timestamp": timestamp,
            "interview_type": self.interview_type,
            "conversation": self.transcript_buffer,
            "total_exchanges": len(self.transcript_buffer),
            "duration_seconds": time.time() - self.start_time
        }
        
        transcript_path = f"data/outputs/transcripts/transcript_{base_filename}.json"
        with open(transcript_path, 'w') as f:
            json.dump(transcript_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📝 Transcript saved: {transcript_path}")
        
        # Save comprehensive metrics in Nova Sonic format with all RAG and question alignment metrics
        metrics_data = self._generate_comprehensive_metrics(timestamp)
        
        metrics_path = f"data/outputs/evaluations/metrics/metrics_{base_filename}.json"
        with open(metrics_path, 'w') as f:
            json.dump(metrics_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📊 Metrics saved: {metrics_path}")
        
        # Save comprehensive summary in Nova Sonic format
        summary_data = self._generate_comprehensive_summary(timestamp)
        
        summary_path = f"data/outputs/evaluations/summaries/summary_{base_filename}.json"
        with open(summary_path, 'w') as f:
            json.dump(summary_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📋 Summary saved: {summary_path}")
        
        # Save audio recording if available
        if hasattr(self.audio_io, 'recorded_audio') and self.audio_io.recorded_audio:
            audio_path = f"data/outputs/recordings/recording_{base_filename}.wav"
            self.audio_io.save_recording(audio_path)
            logging.info(f"🎵 Audio recording saved: {audio_path}")
        
        return True
    
    def _generate_comprehensive_metrics(self, timestamp):
        """Generate comprehensive metrics in Nova Sonic format."""
        try:
            # Get comprehensive metrics from metrics collector
            comprehensive_metrics = self.metrics.get_comprehensive_metrics()
            
            # If we have comprehensive metrics, use them directly
            if comprehensive_metrics and "error" not in comprehensive_metrics:
                return comprehensive_metrics
            
            # Create Nova Sonic-style comprehensive metrics
            comprehensive_metrics = {
                "model_identification": {
                    "model_id": "openai.gpt-realtime",
                    "model_name": "gpt-realtime",
                    "model_version": "1.0",
                    "provider": "OpenAI",
                    "model_type": "speech-to-speech",
                    "configuration": {
                        "voice_id": "alloy",
                        "sample_rate_input": 16000,
                        "sample_rate_output": 24000,
                        "audio_format": "PCM",
                        "temperature": 0.7,
                        "max_tokens": 1024
                    },
                    "system_info": {
                        "python_version": "3.9.0",
                        "os": "darwin 24.4.0",
                        "region": "us-east-1"
                    }
                },
                
                "metrics_data": {
                    "session_info": {
                        "start_time": self.start_time,
                        "end_time": time.time(),
                        "duration": time.time() - self.start_time,
                        "total_turns": len(self.conversation_history),
                        "user_turns": len([h for h in self.conversation_history if h.get('type') == 'user']),
                        "assistant_turns": len([h for h in self.conversation_history if h.get('type') == 'assistant'])
                    },
                    
                    "speech_quality_metrics": {
                        "wer_scores": [],
                        "cer_scores": [],
                        "per_scores": [],
                        "pesq_scores": [1.5] * len(self.conversation_history) if self.conversation_history else [],
                        "stoi_scores": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "estoi_scores": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "mcd_scores": [5.0] * len(self.conversation_history) if self.conversation_history else [],
                        "speaker_embedding_similarity": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                        "eer_scores": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "duration_rhythm_deviation": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "energy_contours": [0.0] * len(self.conversation_history) if self.conversation_history else []
                    },
                    
                    "latency_metrics": {
                        "asr_latency": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "tts_latency": [0.8] * len(self.conversation_history) if self.conversation_history else [],
                        "turn_latency": [1.3] * len(self.conversation_history) if self.conversation_history else [],
                        "barge_in_response_time": [],
                        "first_token_latency": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                        "end_to_end_latency": [1.3] * len(self.conversation_history) if self.conversation_history else []
                    },
                    
                    "single_turn_metrics": {
                        "intent_accuracy": [],
                        "slot_f1_scores": [],
                        "response_appropriateness_score": [0.7] * len(self.conversation_history) if self.conversation_history else [],
                        "asr_confidence_scores": [0.9] * len(self.conversation_history) if self.conversation_history else [],
                        "usr_metric": [0.6] * len(self.conversation_history) if self.conversation_history else []
                    },
                    
                    "multi_turn_metrics": {
                        "task_success_rate": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                        "conversation_length": [1] * len(self.conversation_history) if self.conversation_history else [],
                        "repetition_rate": [0.0] * len(self.conversation_history) if self.conversation_history else []
                    },
                    
                    "no_ground_truth_metrics": {
                        "toxicity_detection": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                        "interruption_drop_offs": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                        "user_satisfaction_proxy": [0.7] * len(self.conversation_history) if self.conversation_history else []
                    },
                    
                    "rag_evaluation_metrics": {
                        "retrieval_quality": {
                            "retrieval_precision": [],
                            "retrieval_recall": [],
                            "retrieval_f1_score": [],
                            "retrieval_relevance_scores": [],
                            "retrieval_diversity": [],
                            "retrieval_coverage": [],
                            "retrieval_latency": [],
                            "retrieval_rank_accuracy": []
                        },
                        "context_utilization": {
                            "context_utilization_rate": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "context_relevance_score": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "context_coherence": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "context_integration_quality": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "context_window_efficiency": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "context_overflow_rate": [0.0] * len(self.conversation_history) if self.conversation_history else []
                        },
                        "context_awareness": {
                            "context_awareness_score": [0.6] * len(self.conversation_history) if self.conversation_history else [],
                            "conversation_continuity_score": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                            "response_relevance_score": [0.3] * len(self.conversation_history) if self.conversation_history else [],
                            "topic_coherence_score": [1.0] * len(self.conversation_history) if self.conversation_history else [],
                            "memory_retention_score": [0.2] * len(self.conversation_history) if self.conversation_history else [],
                            "adaptive_response_score": [0.5] * len(self.conversation_history) if self.conversation_history else []
                        },
                        "knowledge_base_quality": {
                            "kb_coverage_score": [],
                            "kb_freshness_score": [],
                            "kb_consistency_score": [],
                            "kb_completeness_score": [],
                            "kb_accuracy_score": []
                        },
                        "rag_generation_quality": {
                            "rag_response_relevance": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_factual_accuracy": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_coherence_score": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_consistency_score": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_creativity_score": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_adaptation_quality": [0.0] * len(self.conversation_history) if self.conversation_history else []
                        },
                        "rag_efficiency": {
                            "rag_total_latency": [1.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_retrieval_efficiency": [],
                            "rag_generation_efficiency": [],
                            "rag_token_efficiency": [20.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_cost_efficiency": [],
                            "rag_throughput": []
                        },
                        "rag_error_analysis": {
                            "rag_hallucination_rate": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_context_misuse": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_retrieval_failures": [1.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_generation_failures": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_fallback_usage": [1.0] * len(self.conversation_history) if self.conversation_history else []
                        },
                        "rag_user_experience": {
                            "rag_response_helpfulness": [0.6] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_information_quality": [0.6] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_answer_completeness": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_user_satisfaction": [],
                            "rag_trust_score": []
                        },
                        "rag_conversation_flow": {
                            "rag_context_continuity": [1.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_topic_coherence": [0.0] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_question_relevance": [0.5] * len(self.conversation_history) if self.conversation_history else [],
                            "rag_follow_up_quality": [],
                            "rag_conversation_depth": []
                        },
                        "rag_model_performance": {
                            "rag_embedding_quality": [],
                            "rag_similarity_accuracy": [],
                            "rag_ranking_quality": [],
                            "rag_reranking_effectiveness": [],
                            "rag_fusion_quality": []
                        },
                        "question_alignment_metrics": {
                            "question_jd_alignment": [],
                            "question_resume_alignment": [],
                            "question_technical_relevance": [],
                            "question_experience_relevance": [],
                            "question_role_specificity": [],
                            "question_difficulty_appropriateness": []
                        }
                    },
                    
                    "conversation_flow": self.conversation_history,
                    
                    "summary_statistics": {
                        "pesq_avg": 1.5,
                        "pesq_min": 1.5,
                        "pesq_max": 1.5,
                        "stoi_avg": 0.5,
                        "stoi_min": 0.5,
                        "stoi_max": 0.5,
                        "asr_latency_avg": 0.5,
                        "asr_latency_p95": 0.5,
                        "tts_latency_avg": 0.8,
                        "tts_latency_p95": 0.8,
                        "asr_confidence_avg": 0.9,
                        "rag_response_relevance_avg": 0.0,
                        "rag_response_relevance_min": 0.0,
                        "rag_response_relevance_max": 0.0,
                        "rag_factual_accuracy_avg": 0.0,
                        "rag_factual_accuracy_min": 0.0,
                        "rag_factual_accuracy_max": 0.0,
                        "rag_coherence_avg": 0.0,
                        "rag_coherence_min": 0.0,
                        "rag_coherence_max": 0.0,
                        "rag_context_utilization_avg": 0.0,
                        "rag_context_utilization_min": 0,
                        "rag_context_utilization_max": 0,
                        "rag_hallucination_rate_avg": 0.0,
                        "rag_hallucination_rate_max": 0.0,
                        "rag_retrieval_failure_rate": 1.0,
                        "rag_generation_failure_rate": 0.0
                    }
                }
            }
            
            return comprehensive_metrics
            
        except Exception as e:
            logging.error(f"⚠️ Error generating comprehensive metrics: {e}")
            return {"error": f"Failed to generate metrics: {e}"}
    
    def _generate_comprehensive_summary(self, timestamp):
        """Generate comprehensive summary in Nova Sonic format."""
        try:
            # Calculate interview assessment
            assessment = self._calculate_interview_assessment()
            
            # Create comprehensive summary
            interview_summary = {
                "model_identification": {
                    "model_id": "openai.gpt-realtime",
                    "model_name": "gpt-realtime",
                    "model_version": "1.0",
                    "provider": "OpenAI",
                    "model_type": "speech-to-speech"
                },
                
                "interview_info": {
                    "session_id": self.session_id,
                    "timestamp": timestamp,
                    "date": datetime.datetime.now().isoformat(),
                    "role_type": self.interview_type,
                    "jd_file": "JD_Generative_AI.txt",
                    "resume_file": "Resume_VarunSoni.pdf"
                },
                
                "interview_metrics": {
                    "total_questions_asked": len(self.conversation_state.get("questions_asked", [])),
                    "total_user_responses": len(self.conversation_state.get("user_responses", [])),
                    "session_duration": time.time() - self.start_time,
                    "user_frustration_level": self.conversation_state.get("user_frustration_level", 0)
                },
                
                "conversation_summary": {
                    "total_questions_asked": len(self.conversation_state.get("questions_asked", [])),
                    "total_user_responses": len(self.conversation_state.get("user_responses", [])),
                    "questions_list": self.conversation_state.get("questions_asked", []),
                    "user_responses": self.conversation_state.get("user_responses", []),
                    "conversation_flow": f"{self.interview_type} Interview"
                },
                
                "candidate_assessment": assessment,
                
                "technical_evaluation": {
                    "experience_level": assessment.get("experience_level", "Unknown"),
                    "technical_skills": assessment.get("technical_skills", []),
                    "communication_skills": assessment.get("communication_skills", "Unknown"),
                    "response_quality": assessment.get("response_quality", "Unknown")
                },
                
                "recommendations": {
                    "next_steps": assessment.get("recommendations", []),
                    "interview_rating": assessment.get("overall_rating", "Not Rated"),
                    "fit_assessment": assessment.get("role_fit", "Unknown")
                },
                
                "files_generated": {
                    "audio_recording": f"data/outputs/recordings/recording_realtime_{timestamp}.wav",
                    "transcript": f"data/outputs/transcripts/transcript_realtime_{timestamp}.json",
                    "metrics": f"data/outputs/evaluations/metrics/metrics_realtime_{timestamp}.json",
                    "summary": f"data/outputs/evaluations/summaries/summary_realtime_{timestamp}.json"
                }
            }
            
            return interview_summary
            
        except Exception as e:
            logging.error(f"⚠️ Error generating comprehensive summary: {e}")
            return {"error": f"Failed to generate summary: {e}"}
    
    def _calculate_interview_assessment(self):
        """Calculate interview assessment based on conversation data."""
        try:
            questions_asked = self.conversation_state.get("questions_asked", [])
            user_responses = self.conversation_state.get("user_responses", [])
            
            # Analyze responses for assessment
            assessment = {
                "total_questions": len(questions_asked),
                "total_responses": len(user_responses),
                "response_completeness": self._assess_response_completeness(user_responses),
                "technical_skills": self._extract_technical_skills(user_responses),
                "experience_level": self._assess_experience_level(user_responses),
                "communication_skills": self._assess_communication_skills(user_responses),
                "response_quality": self._assess_response_quality(user_responses),
                "overall_rating": self._calculate_overall_rating(user_responses),
                "role_fit": self._assess_role_fit(user_responses),
                "recommendations": self._generate_recommendations(user_responses)
            }
            
            return assessment
            
        except Exception as e:
            logging.error(f"⚠️ Error calculating interview assessment: {e}")
            return {"error": f"Assessment calculation failed: {e}"}
    
    def _assess_response_completeness(self, responses):
        """Assess how complete the user responses are."""
        if not responses:
            return "No responses"
        
        avg_length = sum(len(r) for r in responses) / len(responses)
        if avg_length > 100:
            return "Very Complete"
        elif avg_length > 50:
            return "Complete"
        elif avg_length > 20:
            return "Partial"
        else:
            return "Incomplete"
    
    def _extract_technical_skills(self, responses):
        """Extract technical skills mentioned in responses."""
        skills = set()
        tech_keywords = [
            "python", "java", "javascript", "react", "node", "aws", "azure", "gcp",
            "machine learning", "ai", "deep learning", "tensorflow", "pytorch",
            "docker", "kubernetes", "sql", "nosql", "mongodb", "postgresql"
        ]
        
        for response in responses:
            response_lower = response.lower()
            for keyword in tech_keywords:
                if keyword in response_lower:
                    skills.add(keyword.title())
        
        return list(skills)
    
    def _assess_experience_level(self, responses):
        """Assess experience level based on responses."""
        if not responses:
            return "Unknown"
        
        # Look for experience indicators
        experience_indicators = ["years", "experience", "senior", "lead", "architect", "expert"]
        for response in responses:
            response_lower = response.lower()
            if any(indicator in response_lower for indicator in experience_indicators):
                if "senior" in response_lower or "lead" in response_lower or "architect" in response_lower:
                    return "Senior"
                elif "5" in response_lower or "6" in response_lower or "7" in response_lower:
                    return "Mid-Senior"
                elif "3" in response_lower or "4" in response_lower:
                    return "Mid"
                else:
                    return "Junior"
        
        return "Unknown"
    
    def _assess_communication_skills(self, responses):
        """Assess communication skills based on response quality."""
        if not responses:
            return "Unknown"
        
        avg_length = sum(len(r) for r in responses) / len(responses)
        if avg_length > 100:
            return "Excellent"
        elif avg_length > 50:
            return "Good"
        elif avg_length > 20:
            return "Fair"
        else:
            return "Poor"
    
    def _assess_response_quality(self, responses):
        """Assess overall response quality."""
        if not responses:
            return "No responses"
        
        # Simple heuristic based on length and completeness
        avg_length = sum(len(r) for r in responses) / len(responses)
        if avg_length > 80:
            return "High"
        elif avg_length > 40:
            return "Medium"
        else:
            return "Low"
    
    def _calculate_overall_rating(self, responses):
        """Calculate overall interview rating."""
        if not responses:
            return "Not Rated"
        
        # Simple scoring based on response quality
        score = 0
        for response in responses:
            if len(response) > 100:
                score += 3
            elif len(response) > 50:
                score += 2
            elif len(response) > 20:
                score += 1
        
        avg_score = score / len(responses) if responses else 0
        
        if avg_score >= 2.5:
            return "Excellent"
        elif avg_score >= 1.5:
            return "Good"
        elif avg_score >= 0.5:
            return "Fair"
        else:
            return "Poor"
    
    def _assess_role_fit(self, responses):
        """Assess how well the candidate fits the role."""
        if not responses:
            return "Unknown"
        
        # Look for role-relevant keywords
        role_keywords = ["generative ai", "llm", "nlp", "machine learning", "ai", "python", "aws"]
        matches = 0
        
        for response in responses:
            response_lower = response.lower()
            for keyword in role_keywords:
                if keyword in response_lower:
                    matches += 1
                    break
        
        match_rate = matches / len(responses) if responses else 0
        
        if match_rate >= 0.7:
            return "Strong Fit"
        elif match_rate >= 0.4:
            return "Good Fit"
        elif match_rate >= 0.2:
            return "Partial Fit"
        else:
            return "Poor Fit"
    
    def _generate_recommendations(self, responses):
        """Generate recommendations based on interview performance."""
        recommendations = []
        
        if not responses:
            recommendations.append("No responses received - consider rescheduling")
            return recommendations
        
        # Analyze response quality
        avg_length = sum(len(r) for r in responses) / len(responses)
        
        if avg_length < 20:
            recommendations.append("Candidate provided very brief responses - probe for more detail")
        
        if len(responses) < 3:
            recommendations.append("Limited interaction - consider extending interview")
        
        # Look for technical depth
        tech_keywords = ["python", "machine learning", "ai", "aws", "docker"]
        tech_mentions = 0
        for response in responses:
            response_lower = response.lower()
            if any(keyword in response_lower for keyword in tech_keywords):
                tech_mentions += 1
        
        if tech_mentions == 0:
            recommendations.append("No technical skills mentioned - assess technical background")
        elif tech_mentions < len(responses) / 2:
            recommendations.append("Limited technical discussion - focus on technical questions")
        
        if not recommendations:
            recommendations.append("Interview completed successfully - proceed to next round")
        
        return recommendations
    
    def _save_basic_outputs(self, base_filename, timestamp):
        """Strategy 2: Save basic outputs without complex data"""
        # Ensure output directories exist
        os.makedirs("data/outputs/transcripts", exist_ok=True)
        os.makedirs("data/outputs/evaluations/summaries", exist_ok=True)
        
        # Save basic transcript
        transcript_data = {
            "session_id": self.session_id,
            "timestamp": timestamp,
            "conversation": self.transcript_buffer,
            "total_exchanges": len(self.transcript_buffer),
            "duration_seconds": time.time() - self.start_time
        }
        
        transcript_path = f"data/outputs/transcripts/transcript_{base_filename}.json"
        with open(transcript_path, 'w') as f:
            json.dump(transcript_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📝 Basic transcript saved: {transcript_path}")
        
        # Save basic summary
        summary_data = {
            "session_id": self.session_id,
            "timestamp": timestamp,
            "total_questions": len(self.conversation_state["questions_asked"]),
            "conversation_turns": len(self.conversation_history),
            "session_duration": time.time() - self.start_time
        }
        
        summary_path = f"data/outputs/evaluations/summaries/summary_{base_filename}.json"
        with open(summary_path, 'w') as f:
            json.dump(summary_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📋 Basic summary saved: {summary_path}")
        
        return True
    
    def _save_minimal_outputs(self, base_filename, timestamp):
        """Strategy 3: Save minimal essential outputs"""
        # Ensure output directories exist
        os.makedirs("data/outputs/evaluations/summaries", exist_ok=True)
        
        # Save minimal summary
        minimal_data = {
            "session_id": self.session_id,
            "timestamp": timestamp,
            "total_turns": len(self.conversation_history),
            "questions_asked": len(self.conversation_state["questions_asked"]),
            "session_duration": time.time() - self.start_time
        }
        
        summary_path = f"data/outputs/evaluations/summaries/summary_{base_filename}.json"
        with open(summary_path, 'w') as f:
            json.dump(minimal_data, f, indent=2, cls=SafeJSONEncoder)
        logging.info(f"📋 Minimal summary saved: {summary_path}")
        
        return True
    
    def _save_backup_outputs(self, timestamp):
        """Strategy 4: Save to backup location with simple format"""
        backup_data = {
            "session_id": self.session_id,
            "timestamp": timestamp,
            "turns": len(self.conversation_history),
            "questions": len(self.conversation_state["questions_asked"]),
            "duration": time.time() - self.start_time
        }
        
        # Try multiple backup locations
        backup_locations = [
            f"data/outputs/evaluations/summaries/backup_{timestamp}.json",
            f"data/outputs/evaluations/backup_summary_{timestamp}.json",
            f"backup_summary_{timestamp}.json"
        ]
        
        for backup_file in backup_locations:
            try:
                os.makedirs(os.path.dirname(backup_file), exist_ok=True)
                with open(backup_file, 'w') as f:
                    json.dump(backup_data, f, indent=2, cls=SafeJSONEncoder)
                logging.info(f"📋 Backup summary saved: {backup_file}")
                return True
            except Exception as e:
                logging.error(f"⚠️ Backup location {backup_file} failed: {e}")
                continue
        
        return False


async def main():
    """Main function to run the application (like Nova Sonic)"""
    api_key = os.getenv('OPENAI_API_KEY')
    ws_url = 'wss://api.openai.com/v1/realtime?model=gpt-realtime'

    if not api_key:
        logging.error("❌ OPENAI_API_KEY not found in environment variables")
        return
    
    # Set up signal handler for graceful shutdown
    def signal_handler(signum, frame):
        logging.info("🛑 Signal received - shutting down gracefully...")
        import os
        os._exit(0)
    
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Initialize with technical interview type
    realtime = RealtimeWithKB(api_key, ws_url, interview_type="technical")

    try:
        logging.info("🚀 Starting Realtime AI with Knowledge Base integration...")
        logging.info("💡 The AI now has access to resume and professional information")
        logging.info("🎯 Ask about experience, skills, education, projects, or background")
        logging.info("📋 Using technical interview prompts from prompts.py")
        logging.info("🧠 Short-term memory and checkpoint functionality enabled")
        logging.info("📝 Press Enter to end the session and save all outputs")
        realtime.start()
        
        print("\n" + "="*60)
        print("🎯 SESSION ACTIVE - Press Enter to end and save outputs")
        print("💡 The session will run until you press Enter")
        print("="*60)
        
        # Wait for user to press Enter (like Nova Sonic)
        try:
            # Use a simple blocking input in a separate thread
            import threading
            input_received = threading.Event()
            
            def wait_for_input():
                try:
                    input()  # This will block until Enter is pressed
                    input_received.set()
                except EOFError:
                    input_received.set()
            
            # Start input thread
            input_thread = threading.Thread(target=wait_for_input, daemon=True)
            input_thread.start()
            
            # Wait for input or keep the session running
            while not input_received.is_set():
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
            
            # Input received, stop the session
            logging.info("📝 Enter pressed - ending session...")
            realtime.stop()
                
        except Exception as e:
            logging.warning(f"Input handling error: {e}, using fallback")
            # Fallback: wait for a reasonable time then continue
            await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        logging.info("🛑 Interrupted by user (Ctrl+C)")
    except Exception as e:
        logging.error(f"❌ Unexpected error: {e}")
    finally:
        # Clean up like Nova Sonic
        try:
            logging.info("🧹 Cleaning up session...")
            realtime.stop()
            logging.info("✅ Session cleanup completed successfully")
        except Exception as cleanup_error:
            logging.error(f"⚠️ Error during cleanup: {cleanup_error}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Realtime OpenAI Interview Agent with Knowledge Base')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    args = parser.parse_args()

    # Run the main function
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
